<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'superpower_facility');

// Create connection
function getConnection() {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch(PDOException $e) {
        die("Connection failed: " . $e->getMessage());
    }
}

// Create database and tables if they don't exist
function initializeDatabase() {
    try {
        // First connect without database to create it
        $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create database
        $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME);
        $pdo->exec("USE " . DB_NAME);
        
        // Create appointments table
        $sql = "CREATE TABLE IF NOT EXISTS appointments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            phone VARCHAR(20) NOT NULL,
            service VARCHAR(100) NOT NULL,
            message TEXT,
            appointment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            status ENUM('pending', 'confirmed', 'completed', 'cancelled') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $pdo->exec($sql);
        
        // Create contact_messages table
        $sql = "CREATE TABLE IF NOT EXISTS contact_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            subject VARCHAR(200),
            message TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('unread', 'read', 'replied') DEFAULT 'unread'
        )";
        $pdo->exec($sql);
        
        // Create services table
        $sql = "CREATE TABLE IF NOT EXISTS services (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            icon VARCHAR(50),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $pdo->exec($sql);
        
        // Create testimonials table
        $sql = "CREATE TABLE IF NOT EXISTS testimonials (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            position VARCHAR(100),
            message TEXT NOT NULL,
            image VARCHAR(255) DEFAULT 'images/person_1.jpg',
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $pdo->exec($sql);

        // Create projects table
        $sql = "CREATE TABLE IF NOT EXISTS projects (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            image VARCHAR(255),
            category VARCHAR(100),
            is_featured BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $pdo->exec($sql);

        // Add category column if it doesn't exist (for existing databases)
        try {
            $pdo->exec("ALTER TABLE projects ADD COLUMN category VARCHAR(100) AFTER image");
        } catch(PDOException $e) {
            // Column already exists, ignore error
        }

        // Insert default services only if table is empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM services");
        $service_count = $stmt->fetchColumn();

        if ($service_count == 0) {
            $services = [
                ['Electronic Work', 'Professional electrical installations, repairs, and maintenance services for residential and commercial properties.', 'flaticon-workplace'],
                ['CCTV Installation', 'Advanced security camera systems installation and monitoring solutions for enhanced safety and surveillance.', 'flaticon-pool'],
                ['Patient Care Services', 'Compassionate and professional healthcare assistance, providing quality care and support for patients in need.', 'flaticon-rug'],
                ['Janitorial Services', 'Comprehensive cleaning and maintenance services to keep your facilities spotless and well-maintained.', 'flaticon-kitchen']
            ];

            $stmt = $pdo->prepare("INSERT INTO services (name, description, icon, status) VALUES (?, ?, ?, 'active')");
            foreach ($services as $service) {
                $stmt->execute($service);
            }
        }

        // Insert default testimonials only if table is empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM testimonials");
        $testimonial_count = $stmt->fetchColumn();

        if ($testimonial_count == 0) {
            $testimonials = [
                ['Jennifer Martinez', 'Business Owner', 'Super Power Facility installed our complete CCTV system and electrical work. Their professionalism and attention to detail exceeded our expectations. Highly recommended!', 'images/person_1.jpg'],
                ['Robert Johnson', 'Facility Manager', 'Outstanding janitorial services. They keep our building spotless and their patient care team is exceptional.', 'images/person_2.jpg'],
                ['Sarah Williams', 'Healthcare Director', 'The patient care services provided by Super Power Facility are top-notch. Professional, caring, and reliable.', 'images/person_3.jpg']
            ];

            $stmt = $pdo->prepare("INSERT INTO testimonials (name, position, message, image) VALUES (?, ?, ?, ?)");
            foreach ($testimonials as $testimonial) {
                $stmt->execute($testimonial);
            }
        }

        // Insert default projects only if table is empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM projects");
        $project_count = $stmt->fetchColumn();

        if ($project_count == 0) {
            $projects = [
                ['Electronic Installation', 'Complete electrical system installation for commercial building', 'images/work-1.jpg', 'Electronic Work'],
                ['CCTV Security System', 'Advanced surveillance system with 24/7 monitoring capabilities', 'images/work-2.jpg', 'CCTV Installation'],
                ['Patient Care Services', 'Comprehensive healthcare support and patient assistance program', 'images/work-3.jpg', 'Patient Care'],
                ['Janitorial Services', 'Professional cleaning and maintenance for office complex', 'images/work-4.jpg', 'Janitorial'],
                ['Smart Home Electronics', 'Modern home automation and electrical upgrades', 'images/work-5.jpg', 'Electronic Work'],
                ['Security Camera Network', 'Multi-location CCTV monitoring system', 'images/work-6.jpg', 'CCTV Installation'],
                ['Medical Facility Care', 'Specialized patient care and facility management', 'images/work-7.jpg', 'Patient Care'],
                ['Corporate Cleaning', 'Daily janitorial services for corporate headquarters', 'images/work-8.jpg', 'Janitorial']
            ];

            $stmt = $pdo->prepare("INSERT INTO projects (title, description, image, category) VALUES (?, ?, ?, ?)");
            foreach ($projects as $project) {
                $stmt->execute($project);
            }
        }
        
        return true;
    } catch(PDOException $e) {
        die("Database initialization failed: " . $e->getMessage());
    }
}

// Initialize database on first load
initializeDatabase();
?>
