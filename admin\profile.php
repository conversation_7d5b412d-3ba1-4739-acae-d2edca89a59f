<?php
require_once 'auth.php';
requireLogin();
setSecurityHeaders();

require_once '../config/database.php';

$success_message = '';
$error_message = '';

// Get current user data
$current_user = getCurrentAdmin();

// Handle profile update
if ($_POST && isset($_POST['update_profile'])) {
    $full_name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // Verify CSRF token
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'Invalid security token. Please try again.';
    }
    // Validate input
    elseif (empty($full_name) || empty($email)) {
        $error_message = 'Please fill in all required fields.';
    }
    // Validate email format
    elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'Please enter a valid email address.';
    }
    // If changing password, validate current password
    elseif (!empty($new_password) && !password_verify($current_password, $current_user['password'])) {
        $error_message = 'Current password is incorrect.';
    }
    // Validate new password confirmation
    elseif (!empty($new_password) && $new_password !== $confirm_password) {
        $error_message = 'New password confirmation does not match.';
    }
    // Validate password strength
    elseif (!empty($new_password) && strlen($new_password) < 6) {
        $error_message = 'New password must be at least 6 characters long.';
    }
    else {
        try {
            $pdo = getConnection();
            
            // Check if email is already taken by another user
            $stmt = $pdo->prepare("SELECT id FROM admin_users WHERE email = ? AND id != ?");
            $stmt->execute([$email, $_SESSION['admin_id']]);
            if ($stmt->fetch()) {
                $error_message = 'Email address is already in use by another admin.';
            } else {
                // Update profile
                if (!empty($new_password)) {
                    // Update with new password
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE admin_users SET full_name = ?, email = ?, password = ? WHERE id = ?");
                    $stmt->execute([$full_name, $email, $hashed_password, $_SESSION['admin_id']]);
                } else {
                    // Update without changing password
                    $stmt = $pdo->prepare("UPDATE admin_users SET full_name = ?, email = ? WHERE id = ?");
                    $stmt->execute([$full_name, $email, $_SESSION['admin_id']]);
                }
                
                // Update session data
                $_SESSION['admin_email'] = $email;
                $_SESSION['admin_name'] = $full_name;
                
                $success_message = 'Profile updated successfully!';
                
                // Refresh current user data
                $current_user = getCurrentAdmin();
            }
        } catch(PDOException $e) {
            $error_message = 'Error updating profile: ' . $e->getMessage();
        }
    }
}

// Generate CSRF token
$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - Super Power Facility Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #000;
            color: #fff;
        }
        .card {
            background-color: #1a1a1a;
            border: 1px solid #dc2626;
            color: #fff;
        }
        .card-header {
            background-color: #dc2626;
            border-bottom: 1px solid #dc2626;
        }
        .btn-primary {
            background-color: #dc2626;
            border-color: #dc2626;
        }
        .btn-primary:hover {
            background-color: #991b1b;
            border-color: #991b1b;
        }
        .navbar {
            background-color: #1a1a1a !important;
            border-bottom: 2px solid #dc2626;
        }
        .navbar-brand {
            color: #dc2626 !important;
            font-weight: bold;
        }
        .nav-link {
            color: #fff !important;
        }
        .nav-link:hover {
            color: #dc2626 !important;
        }
        .form-control {
            background-color: #2a2a2a;
            border: 1px solid #dc2626;
            color: #fff;
        }
        .form-control:focus {
            background-color: #2a2a2a;
            border-color: #ef4444;
            color: #fff;
            box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
        }
        .form-control::placeholder {
            color: #999;
        }
        .form-label {
            color: #fff;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-bolt"></i> Super Power Facility Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="appointments.php">
                            <i class="fas fa-calendar"></i> Appointments
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="messages.php">
                            <i class="fas fa-envelope"></i> Messages
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?php echo htmlspecialchars($_SESSION['admin_name']); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark" style="background-color: #1a1a1a; border: 1px solid #dc2626;">
                            <li><a class="dropdown-item active" href="profile.php"><i class="fas fa-user-edit"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cogs"></i> Settings</a></li>
                            <li><hr class="dropdown-divider" style="border-color: #dc2626;"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-user-edit"></i> Profile Settings
                </h1>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user"></i> Update Profile Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($success_message): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="profile.php">
                            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="full_name" class="form-label">Full Name *</label>
                                    <input type="text" name="full_name" id="full_name" class="form-control" 
                                           value="<?php echo htmlspecialchars($current_user['full_name']); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" name="email" id="email" class="form-control" 
                                           value="<?php echo htmlspecialchars($current_user['email']); ?>" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <input type="text" id="username" class="form-control" 
                                           value="<?php echo htmlspecialchars($current_user['username']); ?>" readonly>
                                    <small class="text-muted">Username cannot be changed</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="role" class="form-label">Role</label>
                                    <input type="text" id="role" class="form-control" 
                                           value="<?php echo htmlspecialchars(ucfirst($current_user['role'])); ?>" readonly>
                                </div>
                            </div>

                            <hr style="border-color: #dc2626;">
                            <h6 class="mb-3"><i class="fas fa-lock"></i> Change Password (Optional)</h6>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="current_password" class="form-label">Current Password</label>
                                    <input type="password" name="current_password" id="current_password" class="form-control">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="new_password" class="form-label">New Password</label>
                                    <input type="password" name="new_password" id="new_password" class="form-control">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="confirm_password" class="form-label">Confirm New Password</label>
                                    <input type="password" name="confirm_password" id="confirm_password" class="form-control">
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                                </a>
                                <button type="submit" name="update_profile" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Profile
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i> Account Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Last Login:</strong><br>
                        <?php echo $current_user['last_login'] ? date('M j, Y g:i A', strtotime($current_user['last_login'])) : 'Never'; ?></p>
                        
                        <p><strong>Account Created:</strong><br>
                        <?php echo date('M j, Y', strtotime($current_user['created_at'])); ?></p>
                        
                        <p><strong>Account Status:</strong><br>
                        <span class="badge bg-success">Active</span></p>
                        
                        <hr style="border-color: #dc2626;">
                        
                        <h6><i class="fas fa-shield-alt"></i> Security Tips</h6>
                        <ul class="small">
                            <li>Use a strong password with at least 8 characters</li>
                            <li>Include uppercase, lowercase, numbers, and symbols</li>
                            <li>Don't share your login credentials</li>
                            <li>Log out when finished</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
