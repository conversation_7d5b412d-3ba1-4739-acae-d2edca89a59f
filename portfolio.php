<?php
require_once 'config/database.php';

// Get projects from database
try {
    $pdo = getConnection();
    $stmt = $pdo->query("SELECT * FROM projects ORDER BY id");
    $projects = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    $projects = [];
}
?>
<?php include 'includes/header.php'; ?>

    <div class="hero-wrap hero-wrap-2" style="background-image: url('images/bg_1.jpg');" data-stellar-background-ratio="0.5">
      <div class="overlay"></div>
      <div class="container">
        <div class="row no-gutters slider-text align-items-center justify-content-center" data-scrollax-parent="true">
          <div class="col-md-8 ftco-animate text-center">
          	<p class="breadcrumbs"><span class="mr-2"><a href="index.php">Home</a></span> <span>Portfolio</span></p>
            <h1 class="mb-3 bread">Our Portfolio</h1>
          </div>
        </div>
      </div>
    </div>

    <section class="ftco-section">
      <div class="container">
        <div class="row justify-content-center pb-5 mb-3">
          <div class="col-md-7 heading-section text-center ftco-animate">
          	<span class="subheading">Our Work</span>
            <h2>Recent Projects</h2>
            <p>Explore our portfolio of successful projects across all service categories. Each project demonstrates our commitment to quality and professional excellence.</p>
          </div>
        </div>
        
        <!-- Filter Buttons -->
        <div class="row justify-content-center mb-4">
          <div class="col-md-8 text-center">
            <div class="btn-group" role="group">
              <button type="button" class="btn btn-outline-light filter-btn active" data-filter="all">All Projects</button>
              <button type="button" class="btn btn-outline-light filter-btn" data-filter="Electronic Work">Electronic Work</button>
              <button type="button" class="btn btn-outline-light filter-btn" data-filter="CCTV Installation">CCTV Installation</button>
              <button type="button" class="btn btn-outline-light filter-btn" data-filter="Patient Care">Patient Care</button>
              <button type="button" class="btn btn-outline-light filter-btn" data-filter="Janitorial">Janitorial</button>
            </div>
          </div>
        </div>

        <div class="row" id="portfolio-container">
        <?php foreach($projects as $project): ?>
          <div class="col-md-6 col-lg-4 mb-4 ftco-animate portfolio-item" data-category="<?php echo htmlspecialchars($project['category']); ?>">
            <div class="work img d-flex align-items-center" style="background-image: url(<?php echo htmlspecialchars($project['image']); ?>); height: 300px; background-size: cover; background-position: center;">
            	<a href="<?php echo htmlspecialchars($project['image']); ?>" class="icon image-popup d-flex justify-content-center align-items-center">
    					<span class="fa fa-expand"></span>
    				</a>
            	<div class="desc w-100 px-4 text-center pt-5 mt-5">
	              <div class="text w-100 mb-3 mt-4">
	              	<h3><a href="#" style="color: #dc2626;"><?php echo htmlspecialchars($project['title']); ?></a></h3>
	              	<p class="mb-2"><?php echo htmlspecialchars($project['description']); ?></p>
	              	<span class="badge" style="background: #dc2626;"><?php echo htmlspecialchars($project['category']); ?></span>
	              </div>
              </div>
            </div>
          </div>
        <?php endforeach; ?>
        </div>
      </div>
    </section>

    <section class="ftco-section ftco-bg-dark">
      <div class="container">
        <div class="row justify-content-center pb-5 mb-3">
          <div class="col-md-7 heading-section heading-section-white text-center ftco-animate">
          	<span class="subheading">Project Stats</span>
            <h2>Our Achievements</h2>
          </div>
        </div>
        <div class="row">
          <div class="col-md-3 ftco-animate">
            <div class="text-center">
              <div class="icon d-flex justify-content-center align-items-center mx-auto mb-3" style="width: 80px; height: 80px; background: #dc2626; border-radius: 50%;">
            		<span class="fa fa-bolt" style="font-size: 2rem; color: #fff;"></span>
              </div>
              <h3 style="color: #dc2626;">150+</h3>
              <p>Electronic Projects</p>
            </div>
          </div>
          <div class="col-md-3 ftco-animate">
            <div class="text-center">
              <div class="icon d-flex justify-content-center align-items-center mx-auto mb-3" style="width: 80px; height: 80px; background: #dc2626; border-radius: 50%;">
            		<span class="fa fa-video-camera" style="font-size: 2rem; color: #fff;"></span>
              </div>
              <h3 style="color: #dc2626;">200+</h3>
              <p>CCTV Installations</p>
            </div>
          </div>
          <div class="col-md-3 ftco-animate">
            <div class="text-center">
              <div class="icon d-flex justify-content-center align-items-center mx-auto mb-3" style="width: 80px; height: 80px; background: #dc2626; border-radius: 50%;">
            		<span class="fa fa-heart" style="font-size: 2rem; color: #fff;"></span>
              </div>
              <h3 style="color: #dc2626;">500+</h3>
              <p>Patients Served</p>
            </div>
          </div>
          <div class="col-md-3 ftco-animate">
            <div class="text-center">
              <div class="icon d-flex justify-content-center align-items-center mx-auto mb-3" style="width: 80px; height: 80px; background: #dc2626; border-radius: 50%;">
            		<span class="fa fa-building" style="font-size: 2rem; color: #fff;"></span>
              </div>
              <h3 style="color: #dc2626;">300+</h3>
              <p>Facilities Maintained</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="ftco-section ftco-intro" style="background-image: url('images/bg_3.jpg');" data-stellar-background-ratio="0.5">
    	<div class="overlay"></div>
    	<div class="container">
    		<div class="row justify-content-center">
    			<div class="col-md-8 text-center">
    				<h2>Ready to Start Your Project?</h2>
    				<p class="mb-4">Let us help you bring your vision to life with our professional services.</p>
    				<a href="contact.php" class="btn btn-primary">Start Your Project</a>
    			</div>
    		</div>
    	</div>
    </section>

<style>
.filter-btn {
  border-color: #dc2626 !important;
  color: #dc2626 !important;
  margin: 0 5px 10px 0;
}

.filter-btn.active,
.filter-btn:hover {
  background-color: #dc2626 !important;
  border-color: #dc2626 !important;
  color: #fff !important;
}

.portfolio-item {
  transition: all 0.3s ease;
}

.portfolio-item.hidden {
  display: none;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const filterBtns = document.querySelectorAll('.filter-btn');
  const portfolioItems = document.querySelectorAll('.portfolio-item');

  filterBtns.forEach(btn => {
    btn.addEventListener('click', function() {
      // Remove active class from all buttons
      filterBtns.forEach(b => b.classList.remove('active'));
      // Add active class to clicked button
      this.classList.add('active');

      const filter = this.getAttribute('data-filter');

      portfolioItems.forEach(item => {
        if (filter === 'all' || item.getAttribute('data-category') === filter) {
          item.style.display = 'block';
          item.classList.remove('hidden');
        } else {
          item.style.display = 'none';
          item.classList.add('hidden');
        }
      });
    });
  });
});
</script>

<?php include 'includes/footer.php'; ?>
