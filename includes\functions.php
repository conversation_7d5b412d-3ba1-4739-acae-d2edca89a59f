<?php
require_once 'config/database.php';

// Function to get all services
function getServices() {
    $pdo = getConnection();
    $stmt = $pdo->query("SELECT * FROM services WHERE status = 'active' ORDER BY name");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Function to submit appointment
function submitAppointment($name, $phone, $service, $message = '') {
    $pdo = getConnection();
    $stmt = $pdo->prepare("INSERT INTO appointments (name, phone, service, message) VALUES (?, ?, ?, ?)");
    return $stmt->execute([$name, $phone, $service, $message]);
}

// Function to submit contact message
function submitContactMessage($name, $email, $subject, $message) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("INSERT INTO contact_messages (name, email, subject, message) VALUES (?, ?, ?, ?)");
    return $stmt->execute([$name, $email, $subject, $message]);
}

// Function to get recent appointments
function getRecentAppointments($limit = 10) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("SELECT * FROM appointments ORDER BY created_at DESC LIMIT ?");
    $stmt->execute([$limit]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Function to get appointment statistics
function getAppointmentStats() {
    $pdo = getConnection();
    
    $stats = [];
    
    // Total appointments
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM appointments");
    $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Pending appointments
    $stmt = $pdo->query("SELECT COUNT(*) as pending FROM appointments WHERE status = 'pending'");
    $stats['pending'] = $stmt->fetch(PDO::FETCH_ASSOC)['pending'];
    
    // Completed appointments
    $stmt = $pdo->query("SELECT COUNT(*) as completed FROM appointments WHERE status = 'completed'");
    $stats['completed'] = $stmt->fetch(PDO::FETCH_ASSOC)['completed'];
    
    // This month's appointments
    $stmt = $pdo->query("SELECT COUNT(*) as this_month FROM appointments WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())");
    $stats['this_month'] = $stmt->fetch(PDO::FETCH_ASSOC)['this_month'];
    
    return $stats;
}

// Function to sanitize input
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Function to validate email
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Function to validate phone
function validatePhone($phone) {
    // Remove all non-digit characters
    $phone = preg_replace('/[^0-9]/', '', $phone);
    // Check if it's a valid length (10-15 digits)
    return strlen($phone) >= 10 && strlen($phone) <= 15;
}
?>
