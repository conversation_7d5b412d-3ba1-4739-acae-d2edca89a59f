<?php
require_once 'auth.php';
requireLogin();
setSecurityHeaders();

require_once '../config/database.php';

// Get statistics
try {
    $pdo = getConnection();
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM appointments WHERE status = 'pending'");
    $pending_appointments = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'unread'");
    $unread_messages = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM services WHERE status = 'active'");
    $active_services = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM projects");
    $total_projects = $stmt->fetch()['count'];
    
} catch(PDOException $e) {
    $pending_appointments = 0;
    $unread_messages = 0;
    $active_services = 0;
    $total_projects = 0;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Super Power Facility</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #000;
            color: #fff;
        }
        .card {
            background-color: #1a1a1a;
            border: 1px solid #dc2626;
            color: #fff;
        }
        .card-header {
            background-color: #dc2626;
            border-bottom: 1px solid #dc2626;
        }
        .btn-primary {
            background-color: #dc2626;
            border-color: #dc2626;
        }
        .btn-primary:hover {
            background-color: #991b1b;
            border-color: #991b1b;
        }
        .navbar {
            background-color: #1a1a1a !important;
            border-bottom: 2px solid #dc2626;
        }
        .navbar-brand {
            color: #dc2626 !important;
            font-weight: bold;
        }
        .nav-link {
            color: #fff !important;
        }
        .nav-link:hover {
            color: #dc2626 !important;
        }
        .stat-card {
            background: linear-gradient(135deg, #dc2626, #991b1b);
            border: none;
            color: white;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-bolt"></i> Super Power Facility Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php" target="_blank">
                            <i class="fas fa-external-link-alt"></i> View Website
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="appointments.php">
                            <i class="fas fa-calendar"></i> Appointments
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="messages.php">
                            <i class="fas fa-envelope"></i> Messages
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?php echo htmlspecialchars($_SESSION['admin_name']); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark" style="background-color: #1a1a1a; border: 1px solid #dc2626;">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cogs"></i> Settings</a></li>
                            <li><hr class="dropdown-divider" style="border-color: #dc2626;"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </h1>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-check fa-2x mb-2"></i>
                        <h3><?php echo $pending_appointments; ?></h3>
                        <p class="mb-0">Pending Appointments</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-envelope fa-2x mb-2"></i>
                        <h3><?php echo $unread_messages; ?></h3>
                        <p class="mb-0">Unread Messages</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-cogs fa-2x mb-2"></i>
                        <h3><?php echo $active_services; ?></h3>
                        <p class="mb-0">Active Services</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-project-diagram fa-2x mb-2"></i>
                        <h3><?php echo $total_projects; ?></h3>
                        <p class="mb-0">Total Projects</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar"></i> Recent Appointments
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            $stmt = $pdo->query("SELECT * FROM appointments ORDER BY created_at DESC LIMIT 5");
                            $recent_appointments = $stmt->fetchAll();
                            
                            if (count($recent_appointments) > 0) {
                                foreach ($recent_appointments as $appointment) {
                                    echo '<div class="border-bottom pb-2 mb-2">';
                                    echo '<strong>' . htmlspecialchars($appointment['name']) . '</strong><br>';
                                    echo '<small class="text-muted">' . htmlspecialchars($appointment['service']) . ' - ' . $appointment['created_at'] . '</small>';
                                    echo '</div>';
                                }
                            } else {
                                echo '<p class="text-muted">No appointments yet.</p>';
                            }
                        } catch(PDOException $e) {
                            echo '<p class="text-danger">Error loading appointments.</p>';
                        }
                        ?>
                        <a href="appointments.php" class="btn btn-primary btn-sm mt-2">View All</a>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-envelope"></i> Recent Messages
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            $stmt = $pdo->query("SELECT * FROM contact_messages ORDER BY created_at DESC LIMIT 5");
                            $recent_messages = $stmt->fetchAll();
                            
                            if (count($recent_messages) > 0) {
                                foreach ($recent_messages as $message) {
                                    echo '<div class="border-bottom pb-2 mb-2">';
                                    echo '<strong>' . htmlspecialchars($message['name']) . '</strong><br>';
                                    echo '<small class="text-muted">' . htmlspecialchars($message['subject']) . ' - ' . $message['created_at'] . '</small>';
                                    echo '</div>';
                                }
                            } else {
                                echo '<p class="text-muted">No messages yet.</p>';
                            }
                        } catch(PDOException $e) {
                            echo '<p class="text-danger">Error loading messages.</p>';
                        }
                        ?>
                        <a href="messages.php" class="btn btn-primary btn-sm mt-2">View All</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i> Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="appointments.php" class="btn btn-outline-light w-100">
                                    <i class="fas fa-calendar-plus"></i> Manage Appointments
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="messages.php" class="btn btn-outline-light w-100">
                                    <i class="fas fa-envelope-open"></i> Read Messages
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="services.php" class="btn btn-outline-light w-100">
                                    <i class="fas fa-cog"></i> Edit Services
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="../index.php" target="_blank" class="btn btn-outline-light w-100">
                                    <i class="fas fa-eye"></i> Preview Site
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
