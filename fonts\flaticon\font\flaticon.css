	/*
  	Flaticon icon font: Flaticon
  	Creation date: 05/03/2020 03:23
  	*/

@font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff2") format("woff2"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {   
  font-family: Flaticon;
        font-size: 20px;
font-style: normal;
margin-left: 20px;
}

.flaticon-workplace:before { content: "\f100"; }
.flaticon-pool:before { content: "\f101"; }
.flaticon-rug:before { content: "\f102"; }
.flaticon-kitchen:before { content: "\f103"; }
.flaticon-garden:before { content: "\f104"; }
.flaticon-balcony:before { content: "\f105"; }
.flaticon-cleaning:before { content: "\f106"; }
.flaticon-tap:before { content: "\f107"; }
.flaticon-vacuum-cleaner:before { content: "\f108"; }
.flaticon-sprayer:before { content: "\f109"; }