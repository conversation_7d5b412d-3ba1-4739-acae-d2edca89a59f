<?php
require_once 'auth.php';
setSecurityHeaders();

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: index.php');
    exit();
}

$error_message = '';
$success_message = '';

// Handle login form submission
if ($_POST && isset($_POST['login'])) {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    $user_ip = getUserIP();
    
    // Verify CSRF token
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'Invalid security token. Please try again.';
    }
    // Check rate limiting
    elseif (!checkRateLimit($user_ip)) {
        $error_message = 'Too many login attempts. Please try again in 15 minutes.';
    }
    // Validate input
    elseif (empty($username) || empty($password)) {
        $error_message = 'Please enter both username and password.';
        recordLoginAttempt($user_ip, false);
    }
    // Attempt login
    else {
        // Ensure admin table exists before login attempt
        createAdminTable();

        if (login($username, $password)) {
            recordLoginAttempt($user_ip, true);
            header('Location: index.php');
            exit();
        } else {
            $error_message = 'Invalid username or password. Please check your credentials and try again.';
            recordLoginAttempt($user_ip, false);
        }
    }
}

// Generate CSRF token for form
$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Super Power Facility Admin Panel - Secure login for administrators">
    <meta name="robots" content="noindex, nofollow">
    <meta name="theme-color" content="#dc2626">
    <title>Admin Login - Super Power Facility</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⚡</text></svg>">

    <!-- Stylesheets -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Roboto', sans-serif;
        }
        
        .login-container {
            background: rgba(26, 26, 26, 0.95);
            border: 2px solid #dc2626;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(220, 38, 38, 0.3);
            backdrop-filter: blur(10px);
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-header h1 {
            color: #dc2626;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .login-header p {
            color: #cccccc;
            margin: 0;
        }
        
        .form-control {
            background-color: #2a2a2a;
            border: 1px solid #dc2626;
            color: #ffffff;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
        }
        
        .form-control:focus {
            background-color: #2a2a2a;
            border-color: #ef4444;
            color: #ffffff;
            box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
        }
        
        .form-control::placeholder {
            color: #999999;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #dc2626, #991b1b);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 500;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            background: linear-gradient(135deg, #991b1b, #7f1d1d);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 38, 38, 0.4);
        }
        
        .alert {
            border-radius: 8px;
            border: none;
        }
        
        .alert-danger {
            background-color: rgba(220, 38, 38, 0.1);
            color: #ef4444;
            border: 1px solid #dc2626;
        }
        
        .alert-success {
            background-color: rgba(34, 197, 94, 0.1);
            color: #22c55e;
            border: 1px solid #16a34a;
        }
        
        .input-group-text {
            background-color: #2a2a2a;
            border: 1px solid #dc2626;
            color: #dc2626;
        }
        
        .login-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #333;
        }
        
        .login-footer a {
            color: #dc2626;
            text-decoration: none;
        }
        
        .login-footer a:hover {
            color: #ef4444;
        }
        
        .default-credentials {
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid #dc2626;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        
        .default-credentials h6 {
            color: #dc2626;
            margin-bottom: 0.5rem;
        }
        
        .default-credentials p {
            color: #cccccc;
            margin: 0;
        }

        .login-animation {
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo-animation {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }

        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #dc2626;
            cursor: pointer;
            z-index: 10;
        }

        .password-toggle:hover {
            color: #ef4444;
        }

        .input-group {
            position: relative;
        }

        .security-info {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid #16a34a;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            font-size: 0.85rem;
        }

        .security-info h6 {
            color: #22c55e;
            margin-bottom: 0.5rem;
        }

        .security-info ul {
            color: #cccccc;
            margin: 0;
            padding-left: 1.2rem;
        }

        .security-info li {
            margin-bottom: 0.3rem;
        }
    </style>
</head>
<body>
    <div class="login-container login-animation">
        <div class="login-header">
            <h1 class="logo-animation"><i class="fas fa-bolt"></i> Super Power Facility</h1>
            <p>Admin Panel Login</p>
        </div>
        
        <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Default credentials info -->
        <div class="default-credentials">
            <h6><i class="fas fa-info-circle"></i> Default Login Credentials</h6>
            <p><strong>Username:</strong> admin</p>
            <p><strong>Password:</strong> admin123</p>
        </div>

        <!-- Security information -->
        <div class="security-info">
            <h6><i class="fas fa-shield-alt"></i> Security Features</h6>
            <ul>
                <li>CSRF Protection Enabled</li>
                <li>Rate Limiting Active</li>
                <li>Secure Session Management</li>
                <li>Password Encryption</li>
            </ul>
        </div>
        
        <form method="POST" action="login.php">
            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
            
            <div class="input-group mb-3">
                <span class="input-group-text"><i class="fas fa-user"></i></span>
                <input type="text" name="username" class="form-control" placeholder="Username or Email" 
                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
            </div>
            
            <div class="input-group mb-3">
                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                <input type="password" name="password" id="password" class="form-control" placeholder="Password" required>
                <button type="button" class="password-toggle" onclick="togglePassword()">
                    <i class="fas fa-eye" id="toggleIcon"></i>
                </button>
            </div>
            
            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="remember" style="background-color: #2a2a2a; border-color: #dc2626;">
                <label class="form-check-label" for="remember" style="color: #cccccc;">
                    Remember me
                </label>
            </div>
            
            <button type="submit" name="login" class="btn btn-login">
                <i class="fas fa-sign-in-alt"></i> Login to Admin Panel
            </button>
        </form>
        
        <div class="login-footer">
            <a href="../index.php"><i class="fas fa-arrow-left"></i> Back to Website</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-focus on username field
        document.querySelector('input[name="username"]').focus();

        // Password toggle function
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        // Add loading state to login button
        document.querySelector('form').addEventListener('submit', function() {
            const btn = document.querySelector('.btn-login');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';
            btn.disabled = true;
        });

        // Auto-fill credentials when clicking on default credentials
        document.querySelector('.default-credentials').addEventListener('click', function() {
            document.querySelector('input[name="username"]').value = 'admin';
            document.querySelector('input[name="password"]').value = 'admin123';
        });

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl + Enter to submit form
            if (e.ctrlKey && e.key === 'Enter') {
                document.querySelector('form').submit();
            }
        });

        // Add input validation feedback
        const inputs = document.querySelectorAll('input[required]');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value.trim() === '') {
                    this.style.borderColor = '#dc2626';
                } else {
                    this.style.borderColor = '#22c55e';
                }
            });

            input.addEventListener('input', function() {
                if (this.value.trim() !== '') {
                    this.style.borderColor = '#dc2626';
                }
            });
        });

        // Show/hide security info based on screen size
        function toggleSecurityInfo() {
            const securityInfo = document.querySelector('.security-info');
            if (window.innerWidth < 768) {
                securityInfo.style.display = 'none';
            } else {
                securityInfo.style.display = 'block';
            }
        }

        // Call on load and resize
        window.addEventListener('load', toggleSecurityInfo);
        window.addEventListener('resize', toggleSecurityInfo);
    </script>
</body>
</html>
