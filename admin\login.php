<?php
require_once 'auth.php';
setSecurityHeaders();

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: index.php');
    exit();
}

$error_message = '';
$success_message = '';

// Handle login form submission
if ($_POST && isset($_POST['login'])) {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    $user_ip = getUserIP();
    
    // Verify CSRF token
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'Invalid security token. Please try again.';
    }
    // Check rate limiting
    elseif (!checkRateLimit($user_ip)) {
        $error_message = 'Too many login attempts. Please try again in 15 minutes.';
    }
    // Validate input
    elseif (empty($username) || empty($password)) {
        $error_message = 'Please enter both username and password.';
        recordLoginAttempt($user_ip, false);
    }
    // Attempt login
    else {
        if (login($username, $password)) {
            recordLoginAttempt($user_ip, true);
            header('Location: index.php');
            exit();
        } else {
            $error_message = 'Invalid username or password.';
            recordLoginAttempt($user_ip, false);
        }
    }
}

// Generate CSRF token for form
$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Super Power Facility</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Roboto', sans-serif;
        }
        
        .login-container {
            background: rgba(26, 26, 26, 0.95);
            border: 2px solid #dc2626;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(220, 38, 38, 0.3);
            backdrop-filter: blur(10px);
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-header h1 {
            color: #dc2626;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .login-header p {
            color: #cccccc;
            margin: 0;
        }
        
        .form-control {
            background-color: #2a2a2a;
            border: 1px solid #dc2626;
            color: #ffffff;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
        }
        
        .form-control:focus {
            background-color: #2a2a2a;
            border-color: #ef4444;
            color: #ffffff;
            box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
        }
        
        .form-control::placeholder {
            color: #999999;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #dc2626, #991b1b);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 500;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            background: linear-gradient(135deg, #991b1b, #7f1d1d);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 38, 38, 0.4);
        }
        
        .alert {
            border-radius: 8px;
            border: none;
        }
        
        .alert-danger {
            background-color: rgba(220, 38, 38, 0.1);
            color: #ef4444;
            border: 1px solid #dc2626;
        }
        
        .alert-success {
            background-color: rgba(34, 197, 94, 0.1);
            color: #22c55e;
            border: 1px solid #16a34a;
        }
        
        .input-group-text {
            background-color: #2a2a2a;
            border: 1px solid #dc2626;
            color: #dc2626;
        }
        
        .login-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #333;
        }
        
        .login-footer a {
            color: #dc2626;
            text-decoration: none;
        }
        
        .login-footer a:hover {
            color: #ef4444;
        }
        
        .default-credentials {
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid #dc2626;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        
        .default-credentials h6 {
            color: #dc2626;
            margin-bottom: 0.5rem;
        }
        
        .default-credentials p {
            color: #cccccc;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><i class="fas fa-bolt"></i> Super Power Facility</h1>
            <p>Admin Panel Login</p>
        </div>
        
        <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Default credentials info -->
        <div class="default-credentials">
            <h6><i class="fas fa-info-circle"></i> Default Login Credentials</h6>
            <p><strong>Username:</strong> admin</p>
            <p><strong>Password:</strong> admin123</p>
        </div>
        
        <form method="POST" action="login.php">
            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
            
            <div class="input-group mb-3">
                <span class="input-group-text"><i class="fas fa-user"></i></span>
                <input type="text" name="username" class="form-control" placeholder="Username or Email" 
                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
            </div>
            
            <div class="input-group mb-3">
                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                <input type="password" name="password" class="form-control" placeholder="Password" required>
            </div>
            
            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="remember" style="background-color: #2a2a2a; border-color: #dc2626;">
                <label class="form-check-label" for="remember" style="color: #cccccc;">
                    Remember me
                </label>
            </div>
            
            <button type="submit" name="login" class="btn btn-login">
                <i class="fas fa-sign-in-alt"></i> Login to Admin Panel
            </button>
        </form>
        
        <div class="login-footer">
            <a href="../index.php"><i class="fas fa-arrow-left"></i> Back to Website</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-focus on username field
        document.querySelector('input[name="username"]').focus();
        
        // Add loading state to login button
        document.querySelector('form').addEventListener('submit', function() {
            const btn = document.querySelector('.btn-login');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';
            btn.disabled = true;
        });
    </script>
</body>
</html>
