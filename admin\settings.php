<?php
require_once 'auth.php';
requireLogin();
setSecurityHeaders();

require_once '../config/database.php';

$success_message = '';
$error_message = '';

// Handle settings update
if ($_POST && isset($_POST['update_settings'])) {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // Verify CSRF token
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $success_message = 'Settings updated successfully!';
    }
}

// Generate CSRF token
$csrf_token = generateCSRFToken();

// Get current user
$current_user = getCurrentAdmin();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Super Power Facility Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #000;
            color: #fff;
        }
        .card {
            background-color: #1a1a1a;
            border: 1px solid #dc2626;
            color: #fff;
        }
        .card-header {
            background-color: #dc2626;
            border-bottom: 1px solid #dc2626;
        }
        .btn-primary {
            background-color: #dc2626;
            border-color: #dc2626;
        }
        .btn-primary:hover {
            background-color: #991b1b;
            border-color: #991b1b;
        }
        .navbar {
            background-color: #1a1a1a !important;
            border-bottom: 2px solid #dc2626;
        }
        .navbar-brand {
            color: #dc2626 !important;
            font-weight: bold;
        }
        .nav-link {
            color: #fff !important;
        }
        .nav-link:hover {
            color: #dc2626 !important;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-bolt"></i> Super Power Facility Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="appointments.php">
                            <i class="fas fa-calendar"></i> Appointments
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="messages.php">
                            <i class="fas fa-envelope"></i> Messages
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?php echo htmlspecialchars($_SESSION['admin_name']); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark" style="background-color: #1a1a1a; border: 1px solid #dc2626;">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit"></i> Profile</a></li>
                            <li><a class="dropdown-item active" href="settings.php"><i class="fas fa-cogs"></i> Settings</a></li>
                            <li><hr class="dropdown-divider" style="border-color: #dc2626;"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-cogs"></i> System Settings
                </h1>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-database"></i> Database Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            $pdo = getConnection();
                            
                            // Get database stats
                            $stmt = $pdo->query("SELECT COUNT(*) as count FROM appointments");
                            $appointments_count = $stmt->fetch()['count'];
                            
                            $stmt = $pdo->query("SELECT COUNT(*) as count FROM contact_messages");
                            $messages_count = $stmt->fetch()['count'];
                            
                            $stmt = $pdo->query("SELECT COUNT(*) as count FROM services");
                            $services_count = $stmt->fetch()['count'];
                            
                            $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_users");
                            $admin_count = $stmt->fetch()['count'];
                        ?>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="text-center">
                                    <h3 style="color: #dc2626;"><?php echo $appointments_count; ?></h3>
                                    <p>Total Appointments</p>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="text-center">
                                    <h3 style="color: #dc2626;"><?php echo $messages_count; ?></h3>
                                    <p>Total Messages</p>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="text-center">
                                    <h3 style="color: #dc2626;"><?php echo $services_count; ?></h3>
                                    <p>Active Services</p>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="text-center">
                                    <h3 style="color: #dc2626;"><?php echo $admin_count; ?></h3>
                                    <p>Admin Users</p>
                                </div>
                            </div>
                        </div>
                        <?php
                        } catch(PDOException $e) {
                            echo '<div class="alert alert-danger">Error connecting to database: ' . htmlspecialchars($e->getMessage()) . '</div>';
                        }
                        ?>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt"></i> Security Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-check-circle text-success"></i> Security Features Enabled</h6>
                                <ul>
                                    <li>CSRF Protection</li>
                                    <li>Password Hashing</li>
                                    <li>Session Security</li>
                                    <li>Rate Limiting</li>
                                    <li>SQL Injection Protection</li>
                                    <li>XSS Protection</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-info-circle text-info"></i> System Information</h6>
                                <p><strong>PHP Version:</strong> <?php echo phpversion(); ?></p>
                                <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
                                <p><strong>Database:</strong> MySQL</p>
                                <p><strong>Session ID:</strong> <?php echo substr(session_id(), 0, 10) . '...'; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tools"></i> Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="appointments.php" class="btn btn-outline-light">
                                <i class="fas fa-calendar"></i> Manage Appointments
                            </a>
                            <a href="messages.php" class="btn btn-outline-light">
                                <i class="fas fa-envelope"></i> View Messages
                            </a>
                            <a href="profile.php" class="btn btn-outline-light">
                                <i class="fas fa-user-edit"></i> Edit Profile
                            </a>
                            <a href="../index.php" target="_blank" class="btn btn-outline-light">
                                <i class="fas fa-external-link-alt"></i> View Website
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user"></i> Current Session
                        </h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Logged in as:</strong><br><?php echo htmlspecialchars($current_user['full_name']); ?></p>
                        <p><strong>Username:</strong><br><?php echo htmlspecialchars($current_user['username']); ?></p>
                        <p><strong>Role:</strong><br><?php echo htmlspecialchars(ucfirst($current_user['role'])); ?></p>
                        <p><strong>Last Login:</strong><br><?php echo $current_user['last_login'] ? date('M j, Y g:i A', strtotime($current_user['last_login'])) : 'Never'; ?></p>
                        
                        <hr style="border-color: #dc2626;">
                        
                        <div class="d-grid">
                            <a href="logout.php" class="btn btn-danger">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
