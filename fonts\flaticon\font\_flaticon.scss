    /*
    Flaticon icon font: Flaticon
    Creation date: 05/03/2020 03:23
    */

    @font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff2") format("woff2"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

    .fi:before{
        display: inline-block;
  font-family: "Flaticon";
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  line-height: 1;
  text-decoration: inherit;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
    }

    .flaticon-workplace:before { content: "\f100"; }
.flaticon-pool:before { content: "\f101"; }
.flaticon-rug:before { content: "\f102"; }
.flaticon-kitchen:before { content: "\f103"; }
.flaticon-garden:before { content: "\f104"; }
.flaticon-balcony:before { content: "\f105"; }
.flaticon-cleaning:before { content: "\f106"; }
.flaticon-tap:before { content: "\f107"; }
.flaticon-vacuum-cleaner:before { content: "\f108"; }
.flaticon-sprayer:before { content: "\f109"; }
    
    $font-Flaticon-workplace: "\f100";
    $font-Flaticon-pool: "\f101";
    $font-Flaticon-rug: "\f102";
    $font-Flaticon-kitchen: "\f103";
    $font-Flaticon-garden: "\f104";
    $font-Flaticon-balcony: "\f105";
    $font-Flaticon-cleaning: "\f106";
    $font-Flaticon-tap: "\f107";
    $font-Flaticon-vacuum-cleaner: "\f108";
    $font-Flaticon-sprayer: "\f109";