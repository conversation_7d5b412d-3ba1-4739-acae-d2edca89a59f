<?php
require_once 'config/database.php';

// Get services from database
try {
    $pdo = getConnection();
    $stmt = $pdo->query("SELECT * FROM services WHERE status = 'active' ORDER BY id");
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get testimonials
    $stmt = $pdo->query("SELECT * FROM testimonials WHERE is_active = 1 ORDER BY id LIMIT 5");
    $testimonials = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get projects
    $stmt = $pdo->query("SELECT * FROM projects ORDER BY id LIMIT 8");
    $projects = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch(PDOException $e) {
    $services = [];
    $testimonials = [];
    $projects = [];
}

// Handle appointment form submission
if ($_POST && isset($_POST['submit_appointment'])) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("INSERT INTO appointments (name, phone, service, message) VALUES (?, ?, ?, ?)");
        $stmt->execute([
            $_POST['name'],
            $_POST['phone'], 
            $_POST['service'],
            $_POST['message'] ?? ''
        ]);
        $success_message = "Appointment request submitted successfully!";
    } catch(PDOException $e) {
        $error_message = "Error submitting appointment: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Super Power Facility - Professional Services</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
 
    <link rel="stylesheet" href="css/animate.css">
    
    <link rel="stylesheet" href="css/owl.carousel.min.css">
    <link rel="stylesheet" href="css/owl.theme.default.min.css">
    <link rel="stylesheet" href="css/magnific-popup.css">

    <link rel="stylesheet" href="css/flaticon.css">
    <link rel="stylesheet" href="css/style.css">
  </head>
  <body>
  	<div class="wrap">
			<div class="container">
				<div class="row justify-content-between">
						<div class="col-12 col-md d-flex align-items-center">
							<p class="mb-0 phone"><span class="mailus">Phone no:</span> <a href="#">+****************</a> or <span class="mailus">email us:</span> <a href="#"><EMAIL></a></p>
						</div>
						<div class="col-12 col-md d-flex justify-content-md-end">
							<div class="social-media">
				    		<p class="mb-0 d-flex">
				    			<a href="#" class="d-flex align-items-center justify-content-center"><span class="fa fa-facebook"><i class="sr-only">Facebook</i></span></a>
				    			<a href="#" class="d-flex align-items-center justify-content-center"><span class="fa fa-twitter"><i class="sr-only">Twitter</i></span></a>
				    			<a href="#" class="d-flex align-items-center justify-content-center"><span class="fa fa-instagram"><i class="sr-only">Instagram</i></span></a>
				    			<a href="#" class="d-flex align-items-center justify-content-center"><span class="fa fa-dribbble"><i class="sr-only">Dribbble</i></span></a>
				    		</p>
			        </div>
						</div>
				</div>
			</div>
		</div>
		<nav class="navbar navbar-expand-lg navbar-dark ftco_navbar bg-dark ftco-navbar-light" id="ftco-navbar">
	    <div class="container">
	    	<a class="navbar-brand" href="index.php">Super Power<span>Facility</span></a>
	      <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#ftco-nav" aria-controls="ftco-nav" aria-expanded="false" aria-label="Toggle navigation">
	        <span class="fa fa-bars"></span> Menu
	      </button>
	      <div class="collapse navbar-collapse" id="ftco-nav">
	        <ul class="navbar-nav ml-auto">
	        	<li class="nav-item active"><a href="index.php" class="nav-link">Home</a></li>
	        	<li class="nav-item"><a href="#about" class="nav-link">About</a></li>
	        	<li class="nav-item"><a href="#services" class="nav-link">Services</a></li>
	        	<li class="nav-item"><a href="#projects" class="nav-link">Portfolio</a></li>
	          <li class="nav-item"><a href="#testimonials" class="nav-link">Testimonials</a></li>
	          <li class="nav-item"><a href="contact.php" class="nav-link">Contact</a></li>
	        </ul>
	      </div>
	    </div>
	  </nav>
    <!-- END nav -->
    <div class="hero-wrap js-fullheight" style="background-image: url('images/bg_1.jpg');" data-stellar-background-ratio="0.5">
      <div class="overlay"></div>
      <div class="container">
        <div class="row no-gutters slider-text js-fullheight align-items-center justify-content-start" data-scrollax-parent="true">
          <div class="col-md-6 ftco-animate">
          	<h2 class="subheading">Professional Services You Can Trust</h2>
          	<h1 class="mb-4">Powering Your Needs with Expert Solutions</h1>
            <p><a href="#" class="btn btn-primary mr-md-4 py-2 px-4">Learn more <span class="ion-ios-arrow-forward"></span></a></p>
          </div>
        </div>
      </div>
    </div>

	  <section class="ftco-appointment ftco-section ftco-no-pt ftco-no-pb">
			<div class="overlay"></div>
    	<div class="container">
    		<div class="row d-md-flex justify-content-center">
    			<div class="col-md-12">
	    			<div class="wrap-appointment bg-white d-md-flex pl-md-4 pb-5 pb-md-0">
	    				<?php if (isset($success_message)): ?>
	    					<div class="alert alert-success"><?php echo $success_message; ?></div>
	    				<?php endif; ?>
	    				<?php if (isset($error_message)): ?>
	    					<div class="alert alert-danger"><?php echo $error_message; ?></div>
	    				<?php endif; ?>
	    				<form action="index.php" method="POST" class="appointment w-100">
	    					<div class="row justify-content-center">
								<div class="col-12 col-md d-flex align-items-center pt-4 pt-md-0">
									<div class="form-group py-md-4 py-2 px-4 px-md-0">
										<label for="name">Name</label>
			              <input type="text" name="name" class="form-control" placeholder="Your Name" required>
			            </div>
								</div>
								<div class="col-12 col-md d-flex align-items-center">
									<div class="form-group py-md-4 py-2 px-4 px-md-0">
										<label for="phone">Phone number</label>
			              <input type="text" name="phone" class="form-control" placeholder="Phone number" required>
			            </div>
								</div>
								<div class="col-12 col-md d-flex align-items-center">
									<div class="form-group py-md-4 py-2 px-4 px-md-0">
										<label for="service">Select Services</label>
				    					<div class="form-field">
		          					<div class="select-wrap">
		                      <div class="icon"><span class="fa fa-chevron-down"></span></div>
		                      <select name="service" class="form-control" required>
		                      	<option value="">Select Services</option>
		                      	<?php foreach($services as $service): ?>
		                        <option value="<?php echo htmlspecialchars($service['name']); ?>"><?php echo htmlspecialchars($service['name']); ?></option>
		                        <?php endforeach; ?>
		                      </select>
		                    </div>
				              </div>
				    				</div>
								</div>
								<div class="col-12 col-md d-flex align-items-center pb-4 pb-md-0">
									<div class="form-group py-md-4 py-2 px-4 px-md-0">
										<label for="message">Message</label>
				    					<textarea name="message" class="form-control" placeholder="Additional message (optional)"></textarea>
				    				</div>
								</div>
								<div class="col-12 col-md d-flex align-items-center align-items-stretch">
									<div class="form-group py-md-4 py-2 px-4 px-md-0 d-flex align-items-stretch bg-primary">
			              <input type="submit" name="submit_appointment" value="Make an Appointment" class="btn btn-primary py-3 px-4">
			            </div>
								</div>
	    					</div>
		          </form>
		    		</div>
		    	</div>
    		</div>
    	</div>
    </section>

    <section id="about" class="ftco-section ftco-no-pt ftco-no-pb">
    	<div class="container">
    		<div class="row d-flex no-gutters">
    			<div class="col-md-6 d-flex">
    				<div class="img d-flex align-items-center justify-content-center py-5 py-md-0" style="background-image:url(images/about.jpg);">
    					<div class="time-open-wrap">
    						<div class="desc p-4">
		    					<h2>Business Hours</h2>
			              <div class="opening-hours">
			              	<h4>Opening Days:</h4>
			              	<p class="pl-3">
			              		<span><strong>Monday – Friday:</strong> 9am to 20 pm</span>
			              		<span><strong>Saturday :</strong> 9am to 17 pm</span>
			              	</p>
			              	<h4>Vacations:</h4>
			              	<p class="pl-3">
			              		<span>All Sunday Days</span>
			              		<span>All Official Holidays</span>
			              	</p>
			              </div>
		    				</div>
							<div class="desc p-4 bg-secondary">
								<h3 class="heading">For Emergency Cases</h3>
								<span class="phone">(+01) ************</span>
							</div>
    					</div>
    				</div>
    			</div>
    			<div class="col-md-6 pl-md-5 pt-md-5">
    				<div class="row justify-content-start py-5">
		          <div class="col-md-12 heading-section ftco-animate">
		          	<span class="subheading">Welcome to Super Power Facility</span>
		            <h2 class="mb-4">Empowering Your World with Professional Services</h2>
		            <p>At Super Power Facility, we provide comprehensive professional services including electronic work, CCTV installation, patient care services, and janitorial services. Our expert team is dedicated to delivering reliable, high-quality solutions that meet your specific needs. With years of experience and a commitment to excellence, we ensure every project is completed to the highest standards.</p>
		          </div>
		        </div>
					<div class="row ftco-counter py-5" id="section-counter">
		          <div class="col-md-6 col-lg-4 d-flex justify-content-center counter-wrap ftco-animate">
		            <div class="block-18">
		              <div class="text">
		                <strong class="number" data-number="45">0</strong>
		              </div>
		              <div class="text">
		              	<span>Years of <br>Experienced</span>
		              </div>
		            </div>
		          </div>
		          <div class="col-md-6 col-lg-4 d-flex justify-content-center counter-wrap ftco-animate">
		            <div class="block-18">
		              <div class="text">
		                <strong class="number" data-number="2342">0</strong>
		              </div>
		              <div class="text">
		              	<span>Happy <br>Customers</span>
		              </div>
		            </div>
		          </div>
		          <div class="col-md-6 col-lg-4 d-flex justify-content-center counter-wrap ftco-animate">
		            <div class="block-18">
		              <div class="text">
		                <strong class="number" data-number="30">0</strong>
		              </div>
		              <div class="text">
		              	<span>Projects <br>Completed</span>
		              </div>
		            </div>
		          </div>
		        </div>
	        </div>
        </div>
    	</div>
    </section>

    <section id="services" class="ftco-section">
    	<div class="container">
    		<div class="row justify-content-center pb-5 mb-3">
          <div class="col-md-7 heading-section text-center ftco-animate">
          	<span class="subheading">Services</span>
            <h2>Our Professional Services</h2>
          </div>
        </div>
    		<div class="row">
    		<?php
    		// Limit to only 4 services
    		$limited_services = array_slice($services, 0, 4);
    		foreach($limited_services as $service):
    		?>
          <div class="col-md-6 col-lg-3 mb-4 ftco-animate">
            <div class="card h-100" style="background: #1a1a1a; border: 2px solid #dc2626; color: #fff;">
              <div class="card-body text-center">
                <div class="icon d-flex justify-content-center align-items-center mb-3" style="height: 80px;">
              		<span class="<?php echo htmlspecialchars($service['icon']); ?>" style="font-size: 3rem; color: #dc2626;"></span>
                </div>
                <h4 class="card-title mb-3" style="color: #dc2626;"><?php echo htmlspecialchars($service['name']); ?></h4>
                <p class="card-text"><?php echo htmlspecialchars($service['description']); ?></p>
                <a href="contact.php" class="btn btn-primary mt-auto">Get Quote</a>
              </div>
            </div>
          </div>
        <?php endforeach; ?>
        </div>
    	</div>
    </section>

    <section id="testimonials" class="ftco-section testimony-section ftco-bg-dark">
      <div class="container">
        <div class="row justify-content-center pb-5 mb-3">
          <div class="col-md-7 heading-section heading-section-white text-center ftco-animate">
          	<span class="subheading">Testimonies</span>
            <h2>Happy Customers</h2>
          </div>
        </div>
        <div class="row ftco-animate">
          <div class="col-md-12">
            <div class="carousel-testimony owl-carousel ftco-owl">
            <?php foreach($testimonials as $testimonial): ?>
              <div class="item">
                <div class="testimony-wrap py-4">
                	<div class="icon d-flex align-items-center justify-content-center"><span class="fa fa-quote-right"></span></div>
                  <div class="text">
                  	<div class="d-flex align-items-center mb-4">
                    	<div class="user-img" style="background-image: url(<?php echo htmlspecialchars($testimonial['image']); ?>)"></div>
                    	<div class="pl-3">
		                    <p class="name"><?php echo htmlspecialchars($testimonial['name']); ?></p>
		                    <span class="position"><?php echo htmlspecialchars($testimonial['position']); ?></span>
		                  </div>
	                  </div>
                    <p class="mb-1"><?php echo htmlspecialchars($testimonial['message']); ?></p>
                  </div>
                </div>
              </div>
            <?php endforeach; ?>
            </div>
          </div>
        </div>
      </div>
    </section>

		<section id="projects" class="ftco-section ftco-no-pb">
			<div class="container">
				<div class="row justify-content-center pb-5 mb-3">
          <div class="col-md-12 heading-section  text-center ftco-animate">
          	<span class="subheading">Our Projects</span>
            <h2>Recent Professional Service Projects</h2>
          </div>
        </div>
				<div class="row">
				<?php foreach($projects as $project): ?>
          <div class="col-md-6 col-lg-3 ftco-animate">
            <div class="work img d-flex align-items-center" style="background-image: url(<?php echo htmlspecialchars($project['image']); ?>);">
            	<a href="<?php echo htmlspecialchars($project['image']); ?>" class="icon image-popup d-flex justify-content-center align-items-center">
	    					<span class="fa fa-expand"></span>
	    				</a>
            	<div class="desc w-100 px-4 text-center pt-5 mt-5">
	              <div class="text w-100 mb-3 mt-4">
	              	<h2><a href="portfolio.php"><?php echo htmlspecialchars($project['title']); ?></a></h2>
	              </div>
              </div>
            </div>
          </div>
        <?php endforeach; ?>
        </div>
			</div>
		</section>

    <section class="ftco-section ftco-intro" style="background-image: url('images/bg_3.jpg');" data-stellar-background-ratio="0.5">
    	<div class="overlay"></div>
    	<div class="container">
    		<div class="row justify-content-center">
    			<div class="col-md-8 text-center">
    				<h2>Together we will explore new possibilities</h2>
    				<a href="contact.php" class="icon-video d-flex align-items-center justify-content-center"><span class="fa fa-play"></span></a>
    			</div>
    		</div>
    	</div>
    </section>

    <footer class="footer ftco-section">
			<div class="container">
				<div class="row">
					<div class="col-md-6 col-lg-3 mb-4 mb-md-0">
						<h2 class="footer-heading">Super Power Facility</h2>
						<p>Your trusted partner for professional services including electronic work, CCTV installation, patient care services, and janitorial services. We deliver excellence in every project.</p>
						<ul class="ftco-footer-social list-unstyled float-md-left float-lft mt-4">
              <li class="ftco-animate"><a href="#"><span class="fa fa-twitter"></span></a></li>
              <li class="ftco-animate"><a href="#"><span class="fa fa-facebook"></span></a></li>
              <li class="ftco-animate"><a href="#"><span class="fa fa-instagram"></span></a></li>
            </ul>
					</div>
					<div class="col-md-6 col-lg-3 mb-4 mb-md-0">
						<h2 class="footer-heading">Quick Links</h2>
						<ul class="list-unstyled">
              <li><a href="index.php" class="py-1 d-block">Home</a></li>
              <li><a href="about.php" class="py-1 d-block">About</a></li>
              <li><a href="services.php" class="py-1 d-block">Services</a></li>
              <li><a href="portfolio.php" class="py-1 d-block">Portfolio</a></li>
              <li><a href="blog.php" class="py-1 d-block">Blog</a></li>
              <li><a href="contact.php" class="py-1 d-block">Contact</a></li>
            </ul>
					</div>
					<div class="col-md-6 col-lg-3 pl-lg-5 mb-4 mb-md-0">
						<h2 class="footer-heading">Our Services</h2>
						<ul class="list-unstyled">
						<?php foreach($services as $service): ?>
              <li><a href="services.php" class="py-1 d-block"><?php echo htmlspecialchars($service['name']); ?></a></li>
            <?php endforeach; ?>
            </ul>
					</div>
					<div class="col-md-6 col-lg-3 mb-4 mb-md-0">
						<h2 class="footer-heading">Have Questions?</h2>
						<div class="block-23 mb-3">
              <ul>
                <li><span class="icon fa fa-map-marker"></span><span class="text">123 Power Street, Tech City, California, USA</span></li>
                <li><a href="#"><span class="icon fa fa-phone"></span><span class="text">+****************</span></a></li>
                <li><a href="#"><span class="icon fa fa-paper-plane"></span><span class="text"><EMAIL></span></a></li>
              </ul>
            </div>
					</div>
				</div>
				<div class="row mt-5">
          <div class="col-md-12 text-center">
            <p class="copyright">Copyright &copy;<script>document.write(new Date().getFullYear());</script> Super Power Facility. All rights reserved.</p>
          </div>
        </div>
			</div>
		</footer>

  <!-- loader -->
  <div id="ftco-loader" class="show fullscreen"><svg class="circular" width="48px" height="48px"><circle class="path-bg" cx="24" cy="24" r="22" fill="none" stroke-width="4" stroke="#eeeeee"/><circle class="path" cx="24" cy="24" r="22" fill="none" stroke-width="4" stroke-miterlimit="10" stroke="#F96D00"/></svg></div>

  <script src="js/jquery.min.js"></script>
  <script src="js/jquery-migrate-3.0.1.min.js"></script>
  <script src="js/popper.min.js"></script>
  <script src="js/bootstrap.min.js"></script>
  <script src="js/jquery.easing.1.3.js"></script>
  <script src="js/jquery.waypoints.min.js"></script>
  <script src="js/jquery.stellar.min.js"></script>
  <script src="js/jquery.animateNumber.min.js"></script>
  <script src="js/owl.carousel.min.js"></script>
  <script src="js/jquery.magnific-popup.min.js"></script>
  <script src="js/scrollax.min.js"></script>
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBVWaKrjvy3MaE7SQ74_uJiULgl1JY0H2s&sensor=false"></script>
  <script src="js/google-map.js"></script>
  <script src="js/main.js"></script>

  </body>
</html>
