<?php
require_once 'config/database.php';

// Get services from database
try {
    $pdo = getConnection();
    $stmt = $pdo->query("SELECT * FROM services WHERE status = 'active' ORDER BY id");
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get testimonials
    $stmt = $pdo->query("SELECT * FROM testimonials WHERE is_active = 1 ORDER BY id LIMIT 5");
    $testimonials = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get projects
    $stmt = $pdo->query("SELECT * FROM projects ORDER BY id LIMIT 8");
    $projects = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch(PDOException $e) {
    $services = [];
    $testimonials = [];
    $projects = [];
}

// Handle appointment form submission
if ($_POST && isset($_POST['submit_appointment'])) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("INSERT INTO appointments (name, phone, service, message) VALUES (?, ?, ?, ?)");
        $stmt->execute([
            $_POST['name'],
            $_POST['phone'], 
            $_POST['service'],
            $_POST['message'] ?? ''
        ]);
        $success_message = "Appointment request submitted successfully!";
    } catch(PDOException $e) {
        $error_message = "Error submitting appointment: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Super Power Facility - Professional Services</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
 
    <link rel="stylesheet" href="css/animate.css">
    
    <link rel="stylesheet" href="css/owl.carousel.min.css">
    <link rel="stylesheet" href="css/owl.theme.default.min.css">
    <link rel="stylesheet" href="css/magnific-popup.css">

    <link rel="stylesheet" href="css/flaticon.css">
    <link rel="stylesheet" href="css/style.css">
  </head>
  <body>
  	<div class="wrap">
	
<?php include 'includes/header.php'; ?>
    <!-- END nav -->
    <div class="hero-wrap js-fullheight" style="background-image: url('images/bg_1..jpeg');" data-stellar-background-ratio="0.5">
      <div class="overlay"></div>
      <div class="container">
        <div class="row no-gutters slider-text js-fullheight align-items-center justify-content-start" data-scrollax-parent="true">
          <div class="col-md-6 ftco-animate">
          	<h2 class="subheading">Professional Services You Can Trust</h2>
          	<h1 class="mb-4">Powering Your Needs with Expert Solutions</h1>
            <p><a href="about.php" class="btn btn-primary mr-md-4 py-2 px-4">Learn more <span class="ion-ios-arrow-forward"></span></a></p>
          </div>
        </div>
      </div>
    </div>

	  <section class="ftco-appointment ftco-section ftco-no-pt ftco-no-pb">
			<div class="overlay"></div>
    	<div class="container">
    		<div class="row d-md-flex justify-content-center">
    			<div class="col-md-12">
	    			<div class="wrap-appointment bg-white d-md-flex pl-md-4 pb-5 pb-md-0">
	    				<?php if (isset($success_message)): ?>
	    					<div class="alert alert-success"><?php echo $success_message; ?></div>
	    				<?php endif; ?>
	    				<?php if (isset($error_message)): ?>
	    					<div class="alert alert-danger"><?php echo $error_message; ?></div>
	    				<?php endif; ?>
	    				<form action="index.php" method="POST" class="appointment w-100">
	    					<div class="row justify-content-center">
								<div class="col-12 col-md d-flex align-items-center pt-4 pt-md-0">
									<div class="form-group py-md-4 py-2 px-4 px-md-0">
										<label for="name">Name</label>
			              <input type="text" name="name" class="form-control" placeholder="Your Name" required>
			            </div>
								</div>
								<div class="col-12 col-md d-flex align-items-center">
									<div class="form-group py-md-4 py-2 px-4 px-md-0">
										<label for="phone">Phone number</label>
			              <input type="text" name="phone" class="form-control" placeholder="Phone number" required>
			            </div>
								</div>
								<div class="col-12 col-md d-flex align-items-center">
									<div class="form-group py-md-4 py-2 px-4 px-md-0">
										<label for="service">Select Services</label>
				    					<div class="form-field">
		          					<div class="select-wrap">
		                      <div class="icon"><span class="fa fa-chevron-down"></span></div>
		                      <select name="service" class="form-control" required>
		                      	<option value="">Select Services</option>
		                      	<?php foreach($services as $service): ?>
		                        <option value="<?php echo htmlspecialchars($service['name']); ?>"><?php echo htmlspecialchars($service['name']); ?></option>
		                        <?php endforeach; ?>
		                      </select>
		                    </div>
				              </div>
				    				</div>
								</div>
								<div class="col-12 col-md d-flex align-items-center pb-4 pb-md-0">
									<div class="form-group py-md-4 py-2 px-4 px-md-0">
										<label for="message">Message</label>
				    					<textarea name="message" class="form-control" placeholder="Additional message (optional)"></textarea>
				    				</div>
								</div>
								<div class="col-12 col-md d-flex align-items-center align-items-stretch">
									<div class="form-group py-md-4 py-2 px-4 px-md-0 d-flex align-items-stretch ">
			              <input type="submit" name="submit_appointment" value="Make an Appointment" class="btn btn-primary py-3 px-4">
			            </div>
								</div>
	    					</div>
		          </form>
		    		</div>
		    	</div>
    		</div>
    	</div>
    </section>

    <section id="about" class="ftco-section ftco-no-pt ftco-no-pb">
    	<div class="container">
    		<div class="row d-flex no-gutters">
    			<div class="col-md-6 d-flex">
    				<div class="img d-flex align-items-center justify-content-center py-5 py-md-0" style="background-image:url(images/about.jpg);">
    					<div class="time-open-wrap">
    						<div class="desc p-4" style="background: rgba(248, 0, 0, 0.89); color: #fff;">
		    					<h2>Business Hours</h2>
			              <div class="opening-hours">
			              	<h4>Opening Days:</h4>
			              	<p class="pl-3">
			              		<span><strong></strong> 24/7</span>
			              		
			              	</p>
			              	<h4>Vacations:</h4>
			              	<p class="pl-3">
			              		<span>All Sunday Days</span>
			              		<span>All Official Holidays</span>
			              	</p>
			              </div>
		    				</div>
							<div class="desc p-4 bg-dark">
								<h3 class="heading">For Emergency Cases</h3>
								<span class="phone">0306-916-7771</span>
							</div>
    					</div>
    				</div>
    			</div>
    			<div class="col-md-6 pl-md-5 pt-md-5">
    				<div class="row justify-content-start py-5">
		          <div class="col-md-12 heading-section ftco-animate">
		          	<span class="subheading">Welcome to Super Power Facility</span>
		            <h2 class="mb-4">Empowering Your World with Professional Services</h2>
		            <p>At Super Power Facility, we provide comprehensive professional services including electronic work, CCTV installation, patient care services, and janitorial services. Our expert team is dedicated to delivering reliable, high-quality solutions that meet your specific needs. With years of experience and a commitment to excellence, we ensure every project is completed to the highest standards.</p>
		          </div>
		        </div>
					<div class="row ftco-counter py-5" id="section-counter">
		          <div class="col-md-6 col-lg-4 d-flex justify-content-center counter-wrap ftco-animate">
		            <div class="block-18">
		              <div class="text">
		                <strong class="number" data-number="45">0</strong>
		              </div>
		              <div class="text">
		              	<span>Years of <br>Experienced</span>
		              </div>
		            </div>
		          </div>
		          <div class="col-md-6 col-lg-4 d-flex justify-content-center counter-wrap ftco-animate">
		            <div class="block-18">
		              <div class="text">
		                <strong class="number" data-number="2342">0</strong>
		              </div>
		              <div class="text">
		              	<span>Happy <br>Customers</span>
		              </div>
		            </div>
		          </div>
		          <div class="col-md-6 col-lg-4 d-flex justify-content-center counter-wrap ftco-animate">
		            <div class="block-18">
		              <div class="text">
		                <strong class="number" data-number="30">0</strong>
		              </div>
		              <div class="text">
		              	<span>Projects <br>Completed</span>
		              </div>
		            </div>
		          </div>
		        </div>
	        </div>
        </div>
    	</div>
    </section>

    <section id="services" class="ftco-section">
    	<div class="container">
    		<div class="row justify-content-center pb-5 mb-3">
          <div class="col-md-7 heading-section text-center ftco-animate">
          	<span class="subheading">Services</span>
            <h2>Our Professional Services</h2>
          </div>
        </div>
    		<div class="row">
    		<?php
    		// Limit to only 4 services
    		$limited_services = array_slice($services, 0, 4);
    		foreach($limited_services as $service):
    		?>
          <div class="col-md-6 col-lg-3 mb-4 ftco-animate">
            <div class="card h-100" style="background: #1a1a1a; border: 2px solid #dc2626; color: #fff;">
              <div class="card-body text-center">
                <div class="icon d-flex justify-content-center align-items-center mb-3" style="height: 80px;">
              		<span class="<?php echo htmlspecialchars($service['icon']); ?>" style="font-size: 3rem; color: #dc2626;"></span>
                </div>
                <h4 class="card-title mb-3" style="color: #dc2626;"><?php echo htmlspecialchars($service['name']); ?></h4>
                <p class="card-text"><?php echo htmlspecialchars($service['description']); ?></p>
                <a href="contact.php" class="btn btn-primary mt-auto">Get Quote</a>
              </div>
            </div>
          </div>
        <?php endforeach; ?>
        </div>
    	</div>
    </section>

    <section id="testimonials" class="ftco-section testimony-section ftco-bg-dark">
      <div class="container">
        <div class="row justify-content-center pb-5 mb-3">
          <div class="col-md-7 heading-section heading-section-white text-center ftco-animate">
          	<span class="subheading">Testimonies</span>
            <h2>Happy Customers</h2>
          </div>
        </div>
        <div class="row ftco-animate">
          <div class="col-md-12">
            <div class="carousel-testimony owl-carousel ftco-owl">
            <?php foreach($testimonials as $testimonial): ?>
              <div class="item">
                <div class="testimony-wrap py-4">
                	<div class="icon d-flex align-items-center justify-content-center"><span class="fa fa-quote-right"></span></div>
                  <div class="text">
                  	<div class="d-flex align-items-center mb-4">
                    	<div class="user-img" style="background-image: url(<?php echo htmlspecialchars($testimonial['image']); ?>)"></div>
                    	<div class="pl-3">
		                    <p class="name"><?php echo htmlspecialchars($testimonial['name']); ?></p>
		                    <span class="position"><?php echo htmlspecialchars($testimonial['position']); ?></span>
		                  </div>
	                  </div>
                    <p class="mb-1"><?php echo htmlspecialchars($testimonial['message']); ?></p>
                  </div>
                </div>
              </div>
            <?php endforeach; ?>
            </div>
          </div>
        </div>
      </div>
    </section>

	
				<div class="row">
				
        </div>
			</div>
		</section>

    <section class="ftco-section ftco-intro" style="background-image: url('images/bg_3.jpg');" data-stellar-background-ratio="0.5">
    	<div class="overlay"></div>
    	<div class="container">
    		<div class="row justify-content-center">
    			<div class="col-md-8 text-center">
    				<h2>Together we will explore new possibilities</h2>
    				<a href="contact.php" class="icon-video d-flex align-items-center justify-content-center"><span class="fa fa-play"></span></a>
    			</div>
    		</div>
    	</div>
    </section>

<?php include 'includes/footer.php'; ?>
