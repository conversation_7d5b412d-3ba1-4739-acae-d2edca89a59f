<?php
require_once 'auth.php';
requireLogin();
setSecurityHeaders();

require_once '../config/database.php';

// Handle status updates
if ($_POST && isset($_POST['update_status'])) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("UPDATE contact_messages SET status = ? WHERE id = ?");
        $stmt->execute([$_POST['status'], $_POST['message_id']]);
        $success_message = "Message status updated successfully!";
    } catch(PDOException $e) {
        $error_message = "Error updating status: " . $e->getMessage();
    }
}

// Get all messages
try {
    $pdo = getConnection();
    $stmt = $pdo->query("SELECT * FROM contact_messages ORDER BY created_at DESC");
    $messages = $stmt->fetchAll();
} catch(PDOException $e) {
    $messages = [];
    $error_message = "Error loading messages: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Messages - Super Power Facility Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #000;
            color: #fff;
        }
        .card {
            background-color: #1a1a1a;
            border: 1px solid #dc2626;
            color: #fff;
        }
        .card-header {
            background-color: #dc2626;
            border-bottom: 1px solid #dc2626;
        }
        .btn-primary {
            background-color: #dc2626;
            border-color: #dc2626;
        }
        .btn-primary:hover {
            background-color: #991b1b;
            border-color: #991b1b;
        }
        .navbar {
            background-color: #1a1a1a !important;
            border-bottom: 2px solid #dc2626;
        }
        .navbar-brand {
            color: #dc2626 !important;
            font-weight: bold;
        }
        .nav-link {
            color: #fff !important;
        }
        .nav-link:hover {
            color: #dc2626 !important;
        }
        .table-dark {
            background-color: #1a1a1a;
        }
        .table-dark td, .table-dark th {
            border-color: #dc2626;
        }
        .badge-unread {
            background-color: #fbbf24;
        }
        .badge-read {
            background-color: #10b981;
        }
        .badge-replied {
            background-color: #3b82f6;
        }
        .message-preview {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-bolt"></i> Super Power Facility Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="appointments.php">
                            <i class="fas fa-calendar"></i> Appointments
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="messages.php">
                            <i class="fas fa-envelope"></i> Messages
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php" target="_blank">
                            <i class="fas fa-external-link-alt"></i> View Website
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-envelope"></i> Messages Management
                </h1>
                
                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success"><?php echo $success_message; ?></div>
                <?php endif; ?>
                
                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger"><?php echo $error_message; ?></div>
                <?php endif; ?>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> All Messages (<?php echo count($messages); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (count($messages) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-dark table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Subject</th>
                                            <th>Message</th>
                                            <th>Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($messages as $message): ?>
                                            <tr>
                                                <td><?php echo $message['id']; ?></td>
                                                <td><?php echo htmlspecialchars($message['name']); ?></td>
                                                <td>
                                                    <a href="mailto:<?php echo htmlspecialchars($message['email']); ?>" class="text-decoration-none">
                                                        <?php echo htmlspecialchars($message['email']); ?>
                                                    </a>
                                                </td>
                                                <td><?php echo htmlspecialchars($message['subject']); ?></td>
                                                <td>
                                                    <div class="message-preview" title="<?php echo htmlspecialchars($message['message']); ?>">
                                                        <?php echo htmlspecialchars($message['message']); ?>
                                                    </div>
                                                </td>
                                                <td><?php echo date('M j, Y g:i A', strtotime($message['created_at'])); ?></td>
                                                <td>
                                                    <span class="badge badge-<?php echo $message['status']; ?>">
                                                        <?php echo ucfirst($message['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="message_id" value="<?php echo $message['id']; ?>">
                                                        <select name="status" class="form-select form-select-sm d-inline w-auto" onchange="this.form.submit()">
                                                            <option value="unread" <?php echo $message['status'] == 'unread' ? 'selected' : ''; ?>>Unread</option>
                                                            <option value="read" <?php echo $message['status'] == 'read' ? 'selected' : ''; ?>>Read</option>
                                                            <option value="replied" <?php echo $message['status'] == 'replied' ? 'selected' : ''; ?>>Replied</option>
                                                        </select>
                                                        <input type="hidden" name="update_status" value="1">
                                                    </form>
                                                    <button class="btn btn-sm btn-outline-light ms-1" onclick="showMessage(<?php echo $message['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-envelope-open fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No messages found</h5>
                                <p class="text-muted">Messages will appear here when customers contact you.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Message Modal -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content bg-dark text-white">
                <div class="modal-header border-bottom border-danger">
                    <h5 class="modal-title">Message Details</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageContent">
                    <!-- Message content will be loaded here -->
                </div>
                <div class="modal-footer border-top border-danger">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showMessage(messageId) {
            // Find the message data from the table
            const messages = <?php echo json_encode($messages); ?>;
            const message = messages.find(m => m.id == messageId);
            
            if (message) {
                document.getElementById('messageContent').innerHTML = `
                    <div class="row">
                        <div class="col-md-6"><strong>Name:</strong> ${message.name}</div>
                        <div class="col-md-6"><strong>Email:</strong> ${message.email}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6"><strong>Subject:</strong> ${message.subject}</div>
                        <div class="col-md-6"><strong>Date:</strong> ${new Date(message.created_at).toLocaleString()}</div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <strong>Message:</strong>
                            <div class="mt-2 p-3 border border-secondary rounded">
                                ${message.message}
                            </div>
                        </div>
                    </div>
                `;
                
                new bootstrap.Modal(document.getElementById('messageModal')).show();
            }
        }
    </script>
</body>
</html>
