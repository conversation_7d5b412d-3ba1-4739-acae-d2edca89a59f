# Super Power Facility Website

A professional PHP website with MySQL database for Super Power Facility, offering Electronic Work, CCTV Installation, Patient Care Services, and Janitorial Services.

## Features

- **Black & Red Theme**: Professional dark theme with red accents
- **PHP Backend**: Dynamic content management with MySQL database
- **Responsive Design**: Mobile-friendly Bootstrap-based layout
- **Admin Panel**: Complete management system for appointments and messages
- **Contact Forms**: Appointment booking and contact message systems
- **Service Management**: Dynamic service listings from database

## Database Structure

The website automatically creates the following MySQL tables:

### `services`
- `id` (Primary Key)
- `name` (Service name)
- `description` (Service description)
- `icon` (CSS icon class)
- `status` (active/inactive)
- `created_at` (Timestamp)

### `appointments`
- `id` (Primary Key)
- `name` (Customer name)
- `phone` (Phone number)
- `service` (Selected service)
- `message` (Optional message)
- `appointment_date` (Timestamp)
- `status` (pending/confirmed/completed/cancelled)
- `created_at` (Timestamp)

### `contact_messages`
- `id` (Primary Key)
- `name` (Customer name)
- `email` (Email address)
- `subject` (Message subject)
- `message` (Message content)
- `status` (unread/read/replied)
- `created_at` (Timestamp)

### `testimonials`
- `id` (Primary Key)
- `name` (Customer name)
- `position` (Job title)
- `message` (Testimonial text)
- `image` (Profile image path)
- `is_active` (Boolean)
- `created_at` (Timestamp)

### `projects`
- `id` (Primary Key)
- `title` (Project title)
- `description` (Project description)
- `image` (Project image path)
- `category` (Service category)
- `is_featured` (Boolean)
- `created_at` (Timestamp)

## Setup Instructions

1. **Database Configuration**
   - Update `config/database.php` with your MySQL credentials
   - Default settings: localhost, root, no password, database: `superpower_facility`

2. **File Structure**
   ```
   superpower/
   ├── index.php (Main homepage)
   ├── contact.php (Contact page)
   ├── config/
   │   └── database.php (Database configuration)
   ├── admin/
   │   ├── index.php (Admin dashboard)
   │   ├── appointments.php (Appointment management)
   │   └── messages.php (Message management)
   ├── css/
   │   └── style.css (Custom styling)
   ├── js/ (JavaScript files)
   └── images/ (Image assets)
   ```

3. **Default Services**
   The system automatically creates these services:
   - Electronic Work
   - CCTV Installation
   - Patient Care Services
   - Janitorial Services

## Admin Panel

Access the admin panel at `/admin/index.php`

### Features:
- **Dashboard**: Overview of appointments, messages, and statistics
- **Appointments**: Manage customer appointment requests
- **Messages**: Handle contact form submissions
- **Status Updates**: Change appointment and message statuses

### Admin URLs:
- Dashboard: `/admin/index.php`
- Appointments: `/admin/appointments.php`
- Messages: `/admin/messages.php`

## Theme Customization

The website uses a black and red color scheme defined in CSS variables:

```css
:root {
  --primary-red: #dc2626;
  --dark-red: #991b1b;
  --light-red: #ef4444;
  --black-bg: #000000;
  --dark-gray: #1a1a1a;
  --medium-gray: #2a2a2a;
}
```

## Navigation

- **White Navigation Links**: All navigation links are white as requested
- **Red Hover Effects**: Links turn red on hover
- **Active States**: Current page highlighted in red

## Forms

### Appointment Form (Homepage)
- Customer name (required)
- Phone number (required)
- Service selection (dropdown from database)
- Optional message
- Stores in `appointments` table

### Contact Form (Contact Page)
- Name (required)
- Email (required)
- Subject (required)
- Message (required)
- Stores in `contact_messages` table

## Security Features

- **PDO Prepared Statements**: Protection against SQL injection
- **HTML Escaping**: XSS prevention with `htmlspecialchars()`
- **Input Validation**: Required field validation
- **Error Handling**: Graceful error management

## Browser Compatibility

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile responsive design
- Bootstrap 5 framework
- Font Awesome icons

## Support

For technical support or customization requests, contact the development team.
