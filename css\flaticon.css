/*
  	Flaticon icon font: Flaticon
  	Creation date: 22/02/2020 23:01
  	*/


@font-face {
  font-family: "Flaticon";
  src: url("../fonts/flaticon/font/Flaticon.eot");
  src: url("../fonts/flaticon/font/Flaticon.eot?#iefix") format("embedded-opentype"),
       url("../fonts/flaticon/font/Flaticon.woff") format("woff"),
       url("../fonts/flaticon/font/Flaticon.ttf") format("truetype"),
       url("../fonts/flaticon/font/Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("../fonts/flaticon/font/Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {   
  font-family: Flaticon;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.flaticon-workplace:before { content: "\f100"; }
.flaticon-pool:before { content: "\f101"; }
.flaticon-rug:before { content: "\f102"; }
.flaticon-kitchen:before { content: "\f103"; }
.flaticon-garden:before { content: "\f104"; }
.flaticon-balcony:before { content: "\f105"; }
.flaticon-cleaning:before { content: "\f106"; }
.flaticon-tap:before { content: "\f107"; }
.flaticon-vacuum-cleaner:before { content: "\f108"; }
.flaticon-sprayer:before { content: "\f109"; }