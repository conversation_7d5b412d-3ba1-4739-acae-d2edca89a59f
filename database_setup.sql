-- Super Power Facility Database Setup
CREATE DATABASE IF NOT EXISTS superpower_facility;
USE superpower_facility;

-- Services table
CREATE TABLE IF NOT EXISTS services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Appointments table
CREATE TABLE IF NOT EXISTS appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    service_id INT,
    message TEXT,
    status ENUM('pending', 'confirmed', 'completed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (service_id) REFERENCES services(id)
);

-- Projects table
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(150) NOT NULL,
    description TEXT,
    image VARCHAR(255),
    service_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (service_id) REFERENCES services(id)
);

-- Testimonials table
CREATE TABLE IF NOT EXISTS testimonials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    position VARCHAR(100),
    message TEXT NOT NULL,
    image VARCHAR(255),
    rating INT DEFAULT 5,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Blog posts table
CREATE TABLE IF NOT EXISTS blog_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    excerpt TEXT,
    image VARCHAR(255),
    author VARCHAR(100),
    is_published BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default services
INSERT INTO services (name, description, icon) VALUES
('Electronic Work', 'Professional electrical installations, repairs, and maintenance services for residential and commercial properties.', 'flaticon-workplace'),
('CCTV Installation', 'Advanced security camera systems installation and monitoring solutions for enhanced safety and surveillance.', 'flaticon-pool'),
('Patient Care Services', 'Compassionate and professional healthcare assistance, providing quality care and support for patients in need.', 'flaticon-rug'),
('Janitorial Services', 'Comprehensive cleaning and maintenance services to keep your facilities spotless and well-maintained.', 'flaticon-kitchen');

-- Insert sample projects
INSERT INTO projects (title, description, image, service_id) VALUES
('Electronic Installation', 'Complete electrical system installation for commercial building', 'images/work-1.jpg', 1),
('CCTV Security System', 'Advanced surveillance system with 24/7 monitoring capabilities', 'images/work-2.jpg', 2),
('Patient Care Services', 'Comprehensive healthcare support for elderly care facility', 'images/work-3.jpg', 3),
('Janitorial Services', 'Professional cleaning services for office complex', 'images/work-4.jpg', 4);

-- Insert sample testimonials
INSERT INTO testimonials (name, position, message, image) VALUES
('Jennifer Martinez', 'Business Owner', 'Super Power Facility installed our complete CCTV system and electrical work. Their professionalism and attention to detail exceeded our expectations. Highly recommended!', 'images/person_1.jpg'),
('Robert Johnson', 'Facility Manager', 'Outstanding janitorial services. They keep our building spotless and their patient care team is exceptional.', 'images/person_2.jpg'),
('Sarah Williams', 'Healthcare Director', 'The patient care services provided by Super Power Facility are top-notch. Professional, caring, and reliable.', 'images/person_3.jpg');

-- Insert sample blog posts
INSERT INTO blog_posts (title, content, excerpt, image, author) VALUES
('Essential Electrical Safety Tips', 'Learn about important electrical safety measures for your home and business...', 'Important safety guidelines for electrical installations and maintenance.', 'images/image_1.jpg', 'Michael Rodriguez'),
('Modern CCTV Systems Guide', 'Discover the latest in security camera technology and installation best practices...', 'Complete guide to choosing and installing modern security systems.', 'images/image_2.jpg', 'Sarah Chen'),
('Quality Patient Care Standards', 'Understanding the importance of professional patient care services...', 'Learn about our commitment to providing exceptional patient care.', 'images/image_3.jpg', 'David Thompson');
