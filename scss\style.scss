@import 'bootstrap/bootstrap';
@import 'bootstrap/variables';

$font-primary: '<PERSON><PERSON>',<PERSON><PERSON>, sans-serif;

$white: #fff;
$black: #000000;
$darken: #33313b;

$primary: #2b98f0;
$secondary: #f3e53d;


@mixin border-radius($radius) {
  -webkit-border-radius: $radius;
     -moz-border-radius: $radius;
      -ms-border-radius: $radius;
          border-radius: $radius;
}

@mixin transition($transition) {
    -moz-transition:    all $transition ease;
    -o-transition:      all $transition ease;
    -webkit-transition: all $transition ease;
    -ms-transition: 		all $transition ease;
    transition:         all $transition ease;
}

html {
	// overflow-x: hidden;
}
body {
	font-family: $font-primary;
	background: $white;
	font-size: 16px;
	line-height: 1.8;
	font-weight: 400;
	color: lighten($black,50%);
	&.menu-show {
		overflow: hidden;
		position: fixed;
		height: 100%;
		width: 100%;
	}
}
a {
	transition: .3s all ease;
	color: $primary;
	&:hover, &:focus {
		text-decoration: none;
		color: $primary;
		outline: none !important;
	}
}
h1, h2, h3, h4, h5,
.h1, .h2, .h3, .h4, .h5 {
	line-height: 1.5;
	color: $darken;
	font-weight: 400;
}

.wrap{
	background: darken($primary,10%);
	padding: 0;
	.phone{
		font-size: 12px;
		color: rgba(255,255,255,.8);
		a{
			color: white;
			font-size: 12px;
		}
		.fa{
			color: rgba(255,255,255,.8);
		}
		.mailus{
			color: rgba(255,255,255,.6);	
		}
	}
}
.social-media{
	display: inline-block;
	p{
		a{
			width: 28px;
			height: 28px;
			// background: $primary;
			margin-right: 7px;
			border-radius: 50%;
			span{
				color: $white;
			}
			&:hover{
				span{
					color: $white;
				}
			}
		}
	}
}

.ftco-navbar-light {
	background: rgba(255,255,255,.2)!important;
	// background: rgba(243,229,61,.5)!important;
	position: absolute;
	top: 28px;
	left: 0;
	right: 0;
	z-index: 3;
	padding: 0;
	@include media-breakpoint-down(md) {
		background: $white;
		position: relative;
		top: 0;
		padding: 10px 15px;
	}

	.navbar-brand {
		color: $white;
		text-transform: uppercase;
		font-weight: 700;
		font-size: 20px;
		line-height: 1;
		span{
			color: $secondary;
			position: relative;
		}
		&:hover, &:focus{
			color: $black;
		}
		@include media-breakpoint-down(md){
			color: $black;
			span{
				color: $primary;
			}
		}
	}

	.navbar-nav {
		@include media-breakpoint-down(md){
			padding-bottom: 10px;
		}
		> .nav-item {
			> .nav-link {
				font-size: 14px;
				padding-top: 1.8rem;
				padding-bottom: 1.8rem;
				padding-left: 20px;
				padding-right: 20px;
				font-weight: 400;
				color: rgba(255,255,255,.9);
				position: relative;
				&:before{
					content: "";
				  position: absolute;
				  width: 100%;
				  height: 2px;
				  bottom: 0px;
				  left: 0;
				  background-color: $secondary;
				  visibility: hidden;
				  -webkit-transform: scaleX(0);
				  transform: scaleX(0);
				  -webkit-transition: all 0.5s ease-in-out 0s;
				  transition: all 0.5s ease-in-out 0s;
				  z-index: -1;
				}
				&:hover {
					color: $secondary;
					&:before{
						visibility: visible;
					  background-color: $secondary;
					  -webkit-transform: scaleX(1);
					  transform: scaleX(1);
					}
				}
				opacity: 1!important;
				@include media-breakpoint-down(md){
					padding-left: 0;
					padding-right: 0;
					padding-top: .7rem;
					padding-bottom: .7rem;
					color: rgba(0,0,0,.7);
					&:hover{
						color: $white;
						&:before{
							display: none;
						}
					}
				}
			}

			.dropdown-menu{
				border: none;
				background: $white;
				-webkit-box-shadow: 0px 10px 34px -20px rgba(0,0,0,0.41);
				-moz-box-shadow: 0px 10px 34px -20px rgba(0,0,0,0.41);
				box-shadow: 0px 10px 34px -20px rgba(0,0,0,0.41);
				border-radius: 4px;
				.dropdown-item{
					font-size: 12px;
					color: $black;
					&:hover, &:focus{
						background: $primary;
						color: $white;
					}
				}
				@include media-breakpoint-down(md){
					display: block !important;
					background: $white;
					box-shadow: none;
				}
			}

			
			&.cta {
				> a {
					color: $white;
					background: $primary;
					border-radius: 0px;
					@include media-breakpoint-down(sm){
						padding-left: 15px;
						padding-right: 15px;
					}
					@include media-breakpoint-down(md){
						color: $white;
						background: $primary;
						border-radius: 4px;
					}
				}
			}
			&.active {
				> a {
					color: $secondary;
					&:before{
						visibility: visible;
					  background-color: $secondary;
					  -webkit-transform: scaleX(1);
					  transform: scaleX(1);
					}
					@include media-breakpoint-down(md){
						color: $black;
						&:before{
							display: none;
						}
					}
				}
			}
		}
	}
	
	.navbar-toggler {
		border: none;
		color: rgba(0,0,0,.5)!important;
		cursor: pointer;
		padding-right: 0;
		text-transform: uppercase;
		font-size: 16px;
		letter-spacing: .1em;
		&:focus{
			outline: none !important;
		}
	}
}


.hero-wrap{
	width: 100%;
	height: 100%;
	position: inherit;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: top center;
	position: relative;
	@include media-breakpoint-down(lg){
		background-position: center center !important;
	}
	.overlay{
		position: absolute;
		// width: 50%;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		content: '';
		opacity: .3;
		background: $black;
	}
	&.hero-wrap-2{
		height: 500px;
		.overlay{
			opacity: .3;
		}
		.slider-text{
			height: 500px;
		}
	}
}

.slider-text{
	h1 {
		font-size: 40px;
		color: $white;
		line-height: 1.2;
		font-weight: 400;
		font-family: $font-primary;
		@include media-breakpoint-down(md) {
			font-size: 40px;
		}
		span{
			color: $primary;
		}
	}
	.subheading{
		font-size: 18px;
		line-height: 1.5;
		color: rgba(255,255,255,.7);
	}
	p {
		font-size: 18px;
		line-height: 1.5;
		color: rgba(255,255,255,.7);
		strong{
			font-weight: 700;
			a{
				color: $black;
			}
		}
	}
	.btn-custom{
		font-size: 16px;
		font-weight: 500;
		color: $white;
	}
	.breadcrumbs{
		font-size: 16px;
		font-weight: 400;
		margin-bottom: 20px;
		z-index: 99;
		span{
			color: rgba(255,255,255,.5);
			i{
				color: rgba(255,255,255,.3);
				font-size: 13px;
			}
			a{
				color: rgba(255,255,255,.7);
				&:hover, &:focus{
					color: $primary;
					i{
						color: $primary;
					}
				}
			}
		}
	}
	.bread{
		font-weight: 400 !important;
	}
}

.owl-carousel.owl-drag .owl-item {
	-ms-touch-action: pan-y;
	touch-action: pan-y;
}


//OWL CAROUSEL
.owl-carousel {
	position: relative;
	.owl-item {
		opacity: .4;
		&.active {
			opacity: 1;
		}
	}
	
	.owl-nav {
		position: absolute;
		top: 50%;
		width: 100%;
		.owl-prev,
		.owl-next {
			position: absolute;
			transform: translateY(-50%);
			margin-top: -10px;
			color: $primary !important;
			@include transition(.7s);
			span {
				&:before {
					font-size: 30px;
				}
			}
			opacity: 0;
		}
		.owl-prev {
			left: 0;
		}
		.owl-next {
			right: 0;
		}
	}
	.owl-dots {
		text-align: center;
		.owl-dot {
			width: 10px;
			height: 10px;
			margin: 5px;
			border-radius: 50%;
			background: lighten($black, 90%);
			position: relative;
			&:after{
				position: absolute;
				top: -2px;
				left: -2px;
				right: 0;
				bottom: 0;
				width: 14px;
				height: 14px;
				content: '';
				border:1px solid lighten($black, 90%);
				@include border-radius(50%);
			}	
			&.active {
				background: lighten($black, 70%);
			}
		}
	}
	&:hover{
		.owl-nav{
			.owl-prev,
			.owl-next{
				opacity: 1;
			}
			.owl-prev {
				left: -25px;
			}
			.owl-next {
				right: -25px;
			}
		}
	}
}
.owl-custom-nav {
	float: right;
	position: relative;
	z-index: 10;
	border: 1px solid red;
	.owl-custom-prev,
	.owl-custom-next {
		padding: 10px;
		font-size: 30px;
		background: #ccc;
		line-height: 0;
		width: 60px;
		text-align: center;
		display: inline-block;
	}
} 


.bg-light {
	background: #f8f9fd!important;
}
.bg-secondary{
	background: $secondary !important;
}

.bg-primary{
	background: $primary !important;
}


//BUTTON
.btn {
	padding: 12px 16px;
	cursor: pointer;
	border-radius: 4px;
	box-shadow: none!important;
	font-size: 11px;
	text-transform: uppercase;
	letter-spacing: 1px;
	font-weight: 600;
	&:hover, &:active, &:focus {
		outline: none;
	}
	&.btn-primary {
		background: $primary;
		border: 1px solid $primary;
		color: $white;
		&:hover {
			border: 1px solid $primary;
			background: transparent;
			color :$primary;
		}
		&.btn-outline-primary {
			border: 1px solid $primary;
			background: transparent;
			color :$primary;
			&:hover {
				border: 1px solid transparent;
				background: $primary;
				color :$white;
			}
		}
	}
	&.btn-white {
		background: $white;
		border: 1px solid $white;
		color: $black;
		&:hover {
			border: 1px solid $white;
			background: transparent;
			color :$white;
		}
		&.btn-outline-white {
			border: 1px solid $white;
			background: transparent;
			color :$white;
			&:hover {
				border: 1px solid transparent;
				background: $white;
				color :$black;
			}
		}
	}
	&.btn-secondary {
		background: $secondary;
		border: 1px solid $secondary;
		color: $black;
		&:hover {
			border: 1px solid $secondary;
			background: transparent;
			color :$secondary;
		}
		&.btn-outline-secondary {
			border: 1px solid $secondary;
			background: transparent;
			color :$secondary;
			&:hover {
				border: 1px solid transparent;
				background: $secondary;
				color :$white;
			}
		}
	}
}


//FTCO INTRO
.ftco-intro{
	position: relative;
	overflow: visible;
	z-index: 0;

	position: inherit;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: top center;
	position: relative;
	@include media-breakpoint-down(lg){
		background-position: center center !important;
	}
	.overlay{
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		content: '';
		background: darken($primary,10%);
		opacity: .7;
		z-index: -1;
	}	
	h2{
		color: $white;
		font-weight: 500;
	}
	.icon-video{
		width: 80px;
		height: 80px;
		background: $secondary;
		animation: pulse 2s infinite;
		display: inline-block;
		margin: 0 auto;
		@include border-radius(50%);
		span{
			font-size:20px;
			color: $primary;
		}
	}
}


//SERVICES
.services{
	display: block;
	width: 100%;
	position: relative;
	@include transition(.3s);
	.d-block{
		margin-bottom: 20px;
		&:hover{
			.icon{
				background: $primary;
				&:after{
					bottom: -7px;
					height: 7px;
					left: 4px;
					right: 4px;
				}
				span{
					color: $white;
				}
			}
		}
	}
	.icon{
		line-height: 1.3;
		position: relative;
		width: 70px;
		height: 70px;
		margin: 0 auto;
		position: relative;
		z-index: 0;
		border-radius: 5px;
		background: $primary;
		@include transition(.3s);
		-webkit-box-shadow: 0px 10px 30px -4px rgba(0,0,0,0.15);
		-moz-box-shadow: 0px 10px 30px -4px rgba(0,0,0,0.15);
		box-shadow: 0px 10px 30px -4px rgba(0,0,0,0.15);
		&:after{
			position: absolute;
			bottom: -5px;
			left: 5px;
			right: 5px;
			content: '';
			height: 5px;
			background: $secondary;
			border-radius: 0 0 3px 3px;
			@include transition(.3s);
		}
		span{
			font-size:40px;
			color: $secondary;
		}
	}
	.media-body{
		h3{
			font-weight: 500;
			font-size: 18px;
			color: $black;
		}
	}
	.btn-custom{
		font-size: 12px;
		font-weight: 500;
		text-transform: uppercase;
		color: $black;
		border-bottom: 2px solid lighten($black,90%);
		&:hover{
			color: $secondary;
			border-color: $secondary;
		}
	}
}


#section-counter{
	position: relative;
	z-index: 0;
}
.ftco-counter {
	overflow-x: hidden;
	position: relative;
	background: $secondary;
	@include media-breakpoint-up(lg){
		padding-left: 30px;
		margin-left: -48px;
	}
	.block-18{
		display: block;
		width: 100%;
		@include media-breakpoint-down(md){
			margin-bottom: 2em;
		}
	}
	.text{
		position: relative;
		@include media-breakpoint-down(sm){
			width: 100%;
		}
		.number{
			line-height: 1;
			font-weight: 400;
			font-size: 38px;
			color: $black;
			display: block;
			margin-bottom: 10px;
		}
		span{
			text-transform: uppercase;
			font-weight: 500;
			color: rgba(0,0,0,.7);
		}
	}
	.counter-wrap{
		@include media-breakpoint-down(sm){
			margin-bottom: 20px;
		}
	}
}


//STAFF
.staff{
	width: 100%;
	margin-bottom: 0;
	@include transition(.3s);
	.img-wrap{
		height: 250px;
	}
	.img{
		// height: 300px;
		width: 100%;
		display: block;
		background-position: top center;
		@include transition(.3s);
	}
	.text{
		// width: 94%;
		position: relative;
		background: $primary;
		@include transition(.3s);
		// margin: 0 auto;
		// margin-top: -40px;
		color: rgba(255,255,255,.9);
		z-index: 0;
		border-radius: 0 0 3px 3px;
		&:after{
			position: absolute;
			bottom: -7px;
			left: 7px;
			right: 7px;
			height: 7px;
			content: '';
			background: $secondary;
			border-radius: 0 0 3px 3px;
		}
		h3{
			font-size: 18px;
			font-weight: 700;
			margin-bottom: 0;
			@include transition(.3s);
			color: $white;
			margin-bottom: 5px;
		}
		.position{
			color: rgba(255,255,255,.8);
			display: block;
			font-weight: 500;
			display: inline-block;
			margin-bottom: 10px;
			font-size: 12px;
			text-transform: uppercase;
			letter-spacing: 2px;
		}
		.faded{
			opacity: 1;
		}
	}
	.ftco-social{
		@include transition(.3s);
		li{
			width: 30px;
			height: 30px;
			position: relative;
			a{
				color: $primary;
				width: 30px;
				height: 30px;
				background: $secondary;
				@include border-radius(50%);
				span{
					font-size: 14px;
				}
			}
		}
	}
	&:hover, &:focus{
		.img{
			margin-top: -10px;
		}
		.text{
			background: $secondary;
			&:after{
				background: $primary;
			}
			h3{
				color: $darken;
			}
			.position{
				color: rgba(0,0,0,.5);
			}
		}
		.ftco-social{
			li{
				a{
					background: $primary;
					color: $white;
				}
			}
		}
	}
}


//OPENING TIME
.time-open-wrap{
	border-radius: 5px;
	padding: 0;
	background: $primary;
	overflow: hidden;
	-webkit-box-shadow: 0px 12px 48px -16px rgba(0,0,0,0.29);
	-moz-box-shadow: 0px 12px 48px -16px rgba(0,0,0,0.29);
	box-shadow: 0px 12px 48px -16px rgba(0,0,0,0.29);
	@include media-breakpoint-up(xl){
		position: absolute;
		top: 50%;
		left: -150px;
		transform: translateY(-50%);
	}
	.desc{
		width: 100%;
		h2{
			font-size: 22px;
			font-weight: 700;
			margin-bottom: 10px;
			color: $secondary;
		}
		.heading{
			font-size: 18px;
			font-weight: 700;
			color: $black;
			text-transform: uppercase;
		}
		.phone{
			font-size: 25px;
			color: $black;
			font-weight: 700;
		}
	}
	.opening-hours{
		h4{
			font-size: 14px;
			font-weight: 700;
			color: rgba(255,255,255,.8);
			color: $secondary;
		}
		p{
			span{
				display: block;
				color: rgba(255,255,255,.8);
				strong{
					font-weight: 400;
					color: $white;
				}
			}
		}
	}
}

//BOOK APPOINTMENT
.ftco-appointment{
	background-position: center center !important;
	position: relative;
	.wrap-appointment{
		-webkit-box-shadow: 0px 12px 31px -16px rgba(0,0,0,0.15);
		-moz-box-shadow: 0px 12px 31px -16px rgba(0,0,0,0.15);
		box-shadow: 0px 12px 31px -16px rgba(0,0,0,0.15);
		@include media-breakpoint-up(lg){
			margin-top: -50px;
		}
	}
}

.appointment{
	.form-group{
		position: relative;
		width: 100%;
		display: block;
		margin-bottom: 0;
		label{
			font-size: 12px;
			font-weight: 500;
			color: $primary;
			text-transform: uppercase;
			margin-bottom: 0;
		}
		.icon {
			position: absolute;
			top: 50% !important;
			right: 15px;
			font-size: 16px;
			transform: translateY(-50%);
			span{
				font-size: 14px;
				color: rgba(0,0,0,.5) !important;
			}
		}
		.select-wrap, .input-wrap {
			position: relative;
			select {
				appearance: none;
			}
		}
		.btn{
			width: 100%;
			display: block !important;
		}
	}
	.form-control {
		border: transparent;
		background: $white!important;
		color: rgba(0,0,0,.8)!important;
		font-size: 14px;
		font-weight: 500;
		border-radius: 0;
		box-shadow: none!important;
		padding: 0;
		height: 34px !important;
		border-bottom: 2px solid rgba(0,0,0,.1) !important;
		&::-webkit-input-placeholder { /* Chrome/Opera/Safari */
		  color: rgba(0,0,0,.5);
		}
		&::-moz-placeholder { /* Firefox 19+ */
		  color: rgba(0,0,0,.5);
		}
		&:-ms-input-placeholder { /* IE 10+ */
		  color: rgba(0,0,0,.5);
		}
		&:-moz-placeholder { /* Firefox 18- */
		  color: rgba(0,0,0,.5);
		}
		&:focus, &:active {
		}
	}
	textarea.form-control {
		height: inherit!important;
	}
	.btn-primary{
		&:hover{
			color: $secondary !important;
		}
	}
}


//PROJECT
.work {
  display: block;
  width: 100%;
  height: 270px;
  margin-bottom: 30px;
  background-position: top center !important;
  z-index: 0;
  position: relative;
  overflow: hidden;
  border-radius: 5px;
  &:after{
  	position: absolute;
  	top: 0;
  	left: 0;
  	right: 0;
  	bottom: 0;
  	content: '';
  	background: $primary;
  	opacity: 0;
  	z-index: 0;
  	@include transition(.3s);
  }
  .text{
  	opacity: 0;
  	@include media-breakpoint-down(md){
  		opacity: 1;
  	}
  	span{
  		color: rgba(255,255,255,.5);
  	}
  }
  h2{
  	color: $white;
  	line-height: 1.2;
  	font-size: 16px;
  	text-transform: uppercase;
  	font-weight: 700;
  	letter-spacing: 1px;
  	a{
  		color: $white;
  	}
  }
  .desc{
  	z-index: 1;
  }
  .icon{
 		position: absolute;
 		top: 50%;
 		right: 50%;
 		width: 70px;
 		height: 70px;
 		content: '';
 		z-index: 1;
 		background: $secondary;
 		transform: translate(50%, 50%);
 		margin-top: -70px;
 		opacity: 0;
 		@include border-radius(50%);
 		@include transition(.3s);
 		@include media-breakpoint-down(md){
 			opacity: 1;
 		}
 		span{
 			color: $white;
 		}
 	}
  &:hover{
  	&:after{
  		opacity: .8;
  	}
  	.icon{
  		opacity: 1;
  	}
  	.text{
  		opacity: 1;
  	}
  }
}



//PRICING
.block-7 {
	border-radius: 4px;
	margin-bottom: 30px;
	padding: 30px;
	background: $white;
	-webkit-box-shadow: 0px 24px 48px -13px rgba(0,0,0,0.05);
	-moz-box-shadow: 0px 24px 48px -13px rgba(0,0,0,0.05);
	box-shadow: 0px 24px 48px -13px rgba(0,0,0,0.05);
	@include transition(.3s);
	@include media-breakpoint-down(md){
		margin-top: 30px;
	}
	.heading-2 {
		font-size: 20px;
		color: $black;
		font-weight: 300;
	}
	.price {
		margin: 0;
		padding: 0;
		display: block;
		sup {
			font-size: 24px;
			top: -1em; 
			color: lighten($black,70%);
		}
		.number {
			font-size: 60px;
			font-weight: 300;
			color: $black;
		}
	}
	.excerpt {
		margin-bottom: 10px;
		color: lighten($black,60%);
		font-size: 13px;
		font-weight: 300;
	}
	.label2 {
		text-transform: uppercase;
	}
	.pricing-text {
		&, li {
			padding: 0;
			margin: 0;
		}
		li {
			list-style: none;
			margin-bottom: 15px;
			color: lighten($black,60%);
			span.fa{
				color: $secondary;
			}
		}
	}
	.btn-primary{
		display: inline-block;
	}
	&:hover, &:focus{
		-webkit-box-shadow: 0px 24px 48px -13px rgba(0,0,0,0.11);
		-moz-box-shadow: 0px 24px 48px -13px rgba(0,0,0,0.11);
		box-shadow: 0px 24px 48px -13px rgba(0,0,0,0.11);
	}

}



.ftco-social{
	padding: 0;
	li{
		list-style: none;
		margin-right: 10px;
		display: inline-block;
	}
}


.form-control {
	height: 52px!important;
	background: $white!important;
	color: $black!important;
	font-size: 14px;
	border-radius: 5px;
	box-shadow: none!important;
	&:focus, &:active {
		border-color: $black;
	}
}
textarea.form-control {
	height: inherit!important;
}
.ftco-vh-100 {
  height: 100vh;
  @include media-breakpoint-down(lg) {
  	height: inherit;
  	padding-top: 5em;
  	padding-bottom: 5em;
  }
}

.ftco-animate {
	opacity: 0;
	visibility: hidden;
}



//ABOUT SINGLE

.about-author{
	img{
	}
	.desc{
		h3{
			font-size: 24px;
		}
	}
	.bio{

	}
}


.ftco-section {
	padding: 7em 0;
	position: relative;
	@include media-breakpoint-down(sm){
		padding: 6em 0;
	}
}

.ftco-bg-dark {
	background: $darken !important;
}

.ftco-no-pt{
	padding-top: 0 !important;
}
.ftco-no-pb{
	padding-bottom: 0 !important;
}


//PRICING
.block-7 {
	border-radius: 4px;
	margin-bottom: 30px;
	padding: 30px;
	padding-top: 70px;
	background: $white;
	position: relative;
	-webkit-box-shadow: 0px 24px 48px -13px rgba(0,0,0,0.05);
	-moz-box-shadow: 0px 24px 48px -13px rgba(0,0,0,0.05);
	box-shadow: 0px 24px 48px -13px rgba(0,0,0,0.05);
	@include transition(.3s);
	@include media-breakpoint-down(md){
		margin-top: 30px;
	}
	.icon{
		width: 100px;
		height: 100px;
		position: absolute;
		top: -50px;
		left: 0;
		right: 0;
		margin: 0 auto;
		content: '';
		border-radius: 50%;
		background: $primary;
		-webkit-box-shadow: 0px 15px 34px -29px rgba(0,0,0,0.54);
		-moz-box-shadow: 0px 15px 34px -29px rgba(0,0,0,0.54);
		box-shadow: 0px 15px 34px -29px rgba(0,0,0,0.54);
		span.fa{
			font-size: 40px;
			color: $white;
		}
	}
	.heading-2 {
		font-size: 14px;
		color: $black;
		font-weight: 500;
		text-transform: uppercase;
	}
	.price {
		margin: 0;
		padding: 0;
		display: block;
		sup {
			font-size: 24px;
			top: -0.8em; 
			color: $primary;
		}
		.number {
			font-size: 50px;
			font-weight: 300;
			color: $primary;
		}
	}
	.label2 {
		text-transform: uppercase;
	}
	.pricing-text {
		&, li {
			padding: 0;
			margin: 0;
		}
		li {
			list-style: none;
			margin-bottom: 15px;
			color: lighten($black,60%);
			span.fa{
				color: $secondary;
				font-size: 12px;
			}
		}
	}
	.btn-primary{
		display: inline-block;
	}
	&:hover, &:focus, &.active{
		background: $secondary;
		-webkit-box-shadow: 0px 24px 48px -13px rgba(0,0,0,0.11);
		-moz-box-shadow: 0px 24px 48px -13px rgba(0,0,0,0.11);
		box-shadow: 0px 24px 48px -13px rgba(0,0,0,0.11);
		li{
			color: rgba(0,0,0,.9);
			span.fa{
				color: $primary;
			}
		}
	}

}

//FOOTER 01
.footer{
	background: #111111;
	position: relative;
	z-index: 0;
	overflow: hidden;
	a{
		color: $primary;
	}
	p{
		color: rgba(255,255,255,.6);
	}
	.copyright{
		color:rgba(255,255,255,.3);
		color: lighten($black,60%);
	}
	.footer-heading{
		font-size: 20px;
		margin-bottom: 30px;
		color: $white;
		position: relative;
		font-weight: 700;
		&:after{
			position: absolute;
			bottom: -4px;
			left: 0;
			content: '';
			width: 30px;
			height: 4px;
			background: $secondary;
		}
	}
	ul.list-unstyled{
		li{
			a{
				color: rgba(255,255,255,.7);
			}
		}
	}
	.block-21{
		.text{
			.heading{
				a{
					color: rgba(255,255,255,.7);
				}
			}
		}
		.meta {
			> div {
				a {
					color: $primary;
				}
			}
		}
	}
	.block-23{
		ul{
			li{
				color: rgba(255,255,255,.6);
				a{
					color: rgba(255,255,255,.6);
				}
				.icon{
					color: rgba(255,255,255,.4);
				}
			}
		}
	}
	.ftco-footer-social{
		li{
			a{
				background: $primary;
				color: $white;
			}
		}
	}


	.ftco-footer-social {
		li {
			list-style: none;
			margin: 0 10px 0 0;
			display: inline-block;
			a {
				height: 30px;
				width: 30px;
				display: block;
				background: $primary;
				border-radius: 50%;
				position: relative;
				span {
					position: absolute;
					font-size: 20px;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					color: $white;
				}
				&:hover {
					color: $white;
				}
			}
		}
	}

	//block-21
	.block-21 {
		.img{
			display: block;
			height: 70px;
			width: 70px;
		}
		.text {
			width: calc(100% - 100px);
			.heading {
				font-size: 16px;
				font-weight: 400;
				line-height: 1.4;
				a {
					color: $white;
					&:hover, &:active, &:focus {
						text-decoration: none;
					}
				}
			}
			.meta {
				> div {
					display: inline-block;
					font-size: 13px;
					margin-right: 5px;
					a {
						color: lighten($black, 50%);
					}
				}
			}
		}
	}


	.block-23 {
		ul {
			padding: 0;
			li {
				
				&, > a {
					display: table;
					line-height: 1.5;
					margin-bottom: 15px;
				}
				span{
				}
				.icon, .text {
					display: table-cell;
					vertical-align: top;
				}
				.icon {
					width: 40px;
					font-size: 18px;
					padding-top: 2px;
				}
				
			}
		}
	}
}



@-webkit-keyframes pulse {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba($secondary, 0.4);
  }
  70% {
      -webkit-box-shadow: 0 0 0 30px rgba($secondary, 0);
  }
  100% {
      -webkit-box-shadow: 0 0 0 0 rgba($secondary, 0);
  }
}
@keyframes pulse {
  0% {
    -moz-box-shadow: 0 0 0 0 rgba($secondary, 0.4);
    box-shadow: 0 0 0 0 rgba($secondary, 0.4);
  }
  70% {
      -moz-box-shadow: 0 0 0 30px rgba($secondary, 0);
      box-shadow: 0 0 0 30px rgba($secondary, 0);
  }
  100% {
      -moz-box-shadow: 0 0 0 0 rgba($secondary, 0);
      box-shadow: 0 0 0 0 rgba($secondary, 0);
  }
}

.heading-section{
	.subheading{
		font-size: 12px;
		display: block;
		font-weight: 500;
		color: $primary;
		text-transform: uppercase;
		letter-spacing: 1px;
	}
	h2{
		font-size: 44px;
		font-weight: 300;
		@include media-breakpoint-down(sm){
			font-size: 28px;
		}
	}
	&.heading-section-white{
		.subheading{
			color: rgba(255,255,255,.8);
		}
		h2{
			color: $white;
		}
		p{
			color: rgba(255,255,255,.9);
		}
	}
}

//COVER BG
.img,
.blog-img,
.user-img{
	width: 100%;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center center;
}

.aside-stretch-right{
	background: $primary;
	&:after{
		position: absolute;
		top: 0;
		left: 100%;
		bottom: 0;
		content: '';
		width: 360%;
		background: $primary;
	}
	@include media-breakpoint-down(sm){
		background: transparent;
		&:after{
			background: transparent;
			display: none;
		}
	}
}


//TESTIMONY
.testimony-section{
	position: relative;
	// background: darken($primary,10%);
	.owl-carousel{
		margin: 0;
	}
	.owl-carousel .owl-stage-outer{
		padding: 0 0 2em 0;
		position: relative;
	}
	.owl-nav {
		position: absolute;
		top: 100%;
		width: 100%;
		.owl-prev,
		.owl-next {
			position: absolute;
			transform: translateY(-50%);
			margin-top: -10px;
			outline: none !important;
			@include transition(.3s);
			span {
				&:before {
					font-size: 30px;
					color: rgba(0,0,0,.5);
					@include transition(.3s);
				}
			}
			&:hover,&:focus{
				span{
					&:before{
						color: $white;
					}
				}
			}
			opacity: 0;
		}
		.owl-prev {
			left: 50%;
			margin-left: -80px;
		}
		.owl-next {
			right: 50%;
			margin-right: -80px;
		}
	}
	&:hover{
		.owl-nav{
			.owl-prev,
			.owl-next{
				opacity: 1;
			}
			.owl-prev {
				left: 50%;
				margin-left: -80px;
			}
			.owl-next {
				right: 50%;
				margin-right: -80px;
			}
		}
	}
	.owl-dots {
		text-align: center;
		.owl-dot {
			width: 10px;
			height: 10px;
			margin: 5px;
			border-radius: 50%;
			background: lighten($black,80%);
			&:after{
				border-color: lighten($black,80%);
			}
			&.active {
				background: $secondary;
				&:after{
					border-color: $secondary;
				}
			}
		}
	}
}
.testimony-wrap{
	display: block;
	position: relative;
	padding: 0 20px;
	// border: 1px solid rgba(255,255,255,.1);
	font-size: 16px;
	color: rgba(255,255,255,.8);
	border-radius: 5px;
	background: $primary;
	&:after{
		position: absolute;
		bottom: -5px;
		left: 5px;
		right: 5px;
		content: '';
		height: 5px;
		background: $secondary;
		border-radius: 0 0 3px 3px;
		@include transition(.3s);
	}
	.icon{
		position: absolute;
		top: 20px;
		right: 20px;
		span{
			font-size: 40px;
			color: rgba(255,255,255,.05);
			color: $secondary;
		}
	}
	.user-img{
		width: 70px;
		height: 70px;
		position: relative;
		@include border-radius(50%);
	}
	.name{
		font-weight: 500;
		font-size: 20px;
		margin-bottom: 0;
		color: $white;
	}
	.position{
		font-size: 14px;
		color: rgba(255,255,255,.7);
	}
}




// magnific pop up

.image-popup {
	cursor: -webkit-zoom-in;
	cursor: -moz-zoom-in;
	cursor: zoom-in;
}
.mfp-with-zoom .mfp-container,
.mfp-with-zoom.mfp-bg {
  opacity: 0;
  -webkit-backface-visibility: hidden;
  -webkit-transition: all 0.3s ease-out; 
  -moz-transition: all 0.3s ease-out; 
  -o-transition: all 0.3s ease-out; 
  transition: all 0.3s ease-out;
}

.mfp-with-zoom.mfp-ready .mfp-container {
    opacity: 1;
}
.mfp-with-zoom.mfp-ready.mfp-bg {
    opacity: 0.8;
}

.mfp-with-zoom.mfp-removing .mfp-container, 
.mfp-with-zoom.mfp-removing.mfp-bg {
  opacity: 0;
}



//blocks 
.block-20 {
	overflow: hidden;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center center;
	height: 250px;
	position: relative;
	display: block;
}
.blog-entry{
	@include media-breakpoint-up(md){
		margin-bottom: 40px;
	}
	@include media-breakpoint-down(sm){
		margin-bottom: 30px;
	}
	.text {
		position: relative;
		border-top: 0;
		border-radius: 2px;
		.posted{
			margin-top: -50px;
			display: inline-block;
			padding: 15px 20px;
			position: relative;
			z-index: 0;
			&:after{
				position: absolute;
				top: 0px;
				left: 0px;
				right: 0px;
				bottom: 0px;
				content: '';
				background: $white;
				z-index: -1;
				-webkit-box-shadow: 0px -20px 24px -10px rgba(0,0,0,0.16);
				-moz-box-shadow: 0px -20px 24px -10px rgba(0,0,0,0.16);
				box-shadow: 0px -20px 24px -10px rgba(0,0,0,0.16);
			}
		}
		.author{
			width: 48px;
			height: 48px;
			border-radius: 50%;
		}
		.desc{
			width: calc(100% - 48px);
			span{
				display: block;
				&:first-child(){
					color: lighten($black,70%)
				}
				&:last-child(){
					font-weight: 500;
					color: $primary;
				}
			}
		}
		.heading {
			font-size: 18px;
			margin-bottom: 16px;
			font-weight: 500;
			a {
				color: $black;
				&:hover, &:focus, &:active {
					color: $primary;
				}
			}
		}
	}
	.btn-custom{
		font-size: 12px;
		text-transform: uppercase;
		letter-spacing: 1px;
		font-weight: 500;
		color: darken($primary,10%);
	}
}


.block-23 {
	ul {
		padding: 0;
		li {
			
			&, > a {
				display: table;
				line-height: 1.5;
				margin-bottom: 15px;
			}
			span{
				color: rgba($white, .7);
			}
			.icon, .text {
				display: table-cell;
				vertical-align: top;
			}
			.icon {
				width: 40px;
				font-size: 18px;
				padding-top: 2px;
				color: rgba($white, 1);
			}
			
		}
	}
}

.block-27 {
	ul {
		padding: 0;
		margin: 0;
		li {
			display: inline-block;
			margin-bottom: 4px;
			font-weight: 400;
			a,span {
				color: lighten($black,50%);
				text-align: center;
				display: inline-block;
				width: 40px;
				height: 40px;
				line-height: 40px;
				border-radius: 50%;
				border: 1px solid lighten($black,90%);
			}
			&.active {
				a, span {
					background: $primary;
					color: $white;
					border: 1px solid transparent;
				}
			}
		}
	}
}


//CONTACT FORM
.wrapper{
	width: 100%;
}
.contact-wrap{
	background: $white;
}

.info-wrap{
	@include media-breakpoint-down(sm){
		height: 600px;
	}
}

.dbox{
	width: 100%;
	margin-bottom: 25px;
	@include media-breakpoint-down(sm){
		margin-bottom: 25px !important;
		padding: 0 20px;
	}
	p{
		margin-bottom: 0;
		span{
			font-weight: 500;
			color: $black;
		}
		a{
			color: $primary;
		}
	}
	.icon{
		width: 60px;
		height: 60px;
		border-radius: 4px;
		background: $primary;
		margin: 0 auto;
		margin-bottom: 20px;
		position: relative;
		&:after{
			position: absolute;
			bottom: -5px;
			left: 5px;
			right: 5px;
			content: '';
			height: 5px;
			background: $secondary;
			border-radius: 0 0 3px 3px;
			@include transition(.3s);
		}
		span{
			font-size: 20px;
			color: $white;
		}
	}
	.text{
		width: 100%;
	}
}

.contactForm{
	.label{
		color: $primary;
		text-transform: uppercase;
		font-size: 12px;
		font-weight: 600;
	}
	.form-control{
		border: none;
		border-bottom: 1px solid rgba(0,0,0,.1);
		padding: 0;
	}
}

#map {
	height: 500px;
	width: 100%;
	@include media-breakpoint-down(sm) {
		height: 300px;
	}
}



//### .block-21
.block-21 {
	.blog-img{
		display: block;
		height: 80px;
		width: 80px;
	}
	.text {
		width: calc(100% - 100px);
		.heading {
			font-size: 16px;
			font-weight: 400;
			a {
				color: $black;
				&:hover, &:active, &:focus {
					color: $primary;
				}
			}
		}
		.meta {
			> div {
				display: inline-block;
				font-size: 12px;
				margin-right: 5px;
				font-weight: 400;
				a {
					color: $primary;
				}
			}
		}
	}
}

/* Blog*/
.post-info {
	font-size: 12px;
	text-transform: uppercase;
	font-weight: bold;
	color: $white;
	letter-spacing: .1em;
	> div {
		display: inline-block;

		.seperator {
			display: inline-block;
			margin: 0 10px;
			opacity: .5;
		}
	}
}

.tagcloud {
	a {
		text-transform: uppercase;
		display: inline-block;
		padding: 4px 10px;
		margin-bottom: 7px;
		margin-right: 4px;
		border-radius: 4px;
		color: $black;
		border: 1px solid #ccc;
		font-size :11px;
		&:hover {
			border: 1px solid #000;
		}
	}
}

.comment-form-wrap {
	clear: both;
}

.comment-list {
	padding: 0;
	margin: 0;
	.children {
		padding: 50px 0 0 40px;
		margin: 0;
		float: left;
		width: 100%;
	}
	li {
		padding: 0;
		margin: 0 0 30px 0;
		float: left;
		width: 100%;
		clear: both;
		list-style: none;
		.vcard {
			width: 80px;
			float: left;
			img {
				width: 50px;
				border-radius: 50%;
			}
		}
		.comment-body {
			float: right;
			width: calc(100% - 80px);
			h3 {
				font-size: 20px;
			}
			.meta {
				text-transform: uppercase;
				font-size: 13px;
				letter-spacing: .1em;
				color: #ccc;
			}
			.reply {
				padding: 5px 10px;
				background: lighten($black, 90%);
				color: $black;
				text-transform: uppercase;
				font-size: 11px;
				letter-spacing: .1em;
				font-weight: 400;
				border-radius: 4px;
				&:hover {
					color: $white;
					background: lighten($black, 0%);
				}
			}
		}
	}
}

.search-form {
	.form-group {
		position: relative;

		input {
			padding-right: 50px;
			font-size: 14px;
		}
	}
	.fa {
		position: absolute;
		top: 50%;
		right: 20px;
		transform: translateY(-50%);
	}
}



// SIDEBAR
.sidebar-box {
	margin-bottom: 40px;
	font-size: 15px;
	width: 100%;
	
	float: left;
	
	background: $white;
	*:last-child {
		margin-bottom: 0;
	}
	h3 {
		font-size: 18px;
		margin-bottom: 15px;
		font-weight: 700;
	}
}

.categories, .sidelink {
	li {
		position: relative;
		margin-bottom: 10px;
		padding-bottom: 10px;
		border-bottom: 1px solid gray('300');
		list-style: none;
		&:last-child {
			margin-bottom: 0;
			border-bottom: none;
			padding-bottom: 0;
		}
		a {
			display: block;
			color: lighten($black,10%);
			span {
				position: absolute;
				right: 0;
				top: 50%;
				transform: translateY(-50%);
				color: #ccc;
				font-size: 12px;
			}
			&:hover,&:focus{
				outline: none;
				color: $primary;
			}
		}
		&.active {
			a {
				color: $primary;
				span{
					color: $primary;
				}
			}
		}
	}
}


#ftco-loader {
	position: fixed;
	width:  96px;
	height: 96px;
	left:  50%;
	top: 50%;
	transform: translate(-50%, -50%);
	background-color: rgba(255,255,255,0.9);
	box-shadow: 0px 24px 64px rgba(0,0,0,0.24);
	border-radius:16px;
	opacity: 0; 
	visibility: hidden;
	transition: opacity .2s ease-out, visibility 0s linear .2s;
	z-index:1000;
}

#ftco-loader.fullscreen {
	padding:  0;
	left:  0;
	top:  0;
	width:  100%;
	height: 100%;
	transform: none;
	background-color: #fff;
	border-radius: 0;
	box-shadow: none;
}

#ftco-loader.show {
	transition: opacity .4s ease-out, visibility 0s linear 0s;
	visibility: visible;
	opacity: 1;
}

#ftco-loader .circular {
  animation: loader-rotate 2s linear infinite;
  position: absolute;
  left:  calc(50% - 24px);
  top:  calc(50% - 24px);
  display: block;
  transform: rotate(0deg);
}

#ftco-loader .path {
  stroke-dasharray: 1, 200;
  stroke-dashoffset: 0;
  animation: loader-dash 1.5s ease-in-out infinite;
  stroke-linecap: round;
}

@keyframes loader-rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loader-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -136px;
  }
}